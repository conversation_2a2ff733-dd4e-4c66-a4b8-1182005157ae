#!/usr/bin/env python3
"""
调试ImageDecoder的tf.function问题
"""

import tensorflow as tf
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import ImageJSCCConfig
from layers.image_decoder import ImageDecoder

def test_decoder():
    """测试解码器是否有tf.function问题"""
    print("🔧 测试ImageDecoder...")
    
    # 创建配置
    config = ImageJSCCConfig()
    print(f"配置: 压缩比={config.compression_ratio}, 嵌入维度={config.embedding_dim}")
    
    # 创建解码器
    decoder = ImageDecoder(config)
    print("✅ 解码器创建成功")
    
    # 创建测试输入
    batch_size = 2
    compressed_features = 2048  # 根据配置计算的压缩特征维度
    test_input = tf.random.normal([batch_size, compressed_features])
    print(f"测试输入形状: {test_input.shape}")
    
    try:
        # 第一次调用 - 这里可能会出现tf.function错误
        print("🚀 第一次前向传播...")
        output1 = decoder(test_input, training=True)
        print(f"✅ 第一次调用成功，输出形状: {output1.shape}")
        
        # 第二次调用 - 测试是否有重复创建层的问题
        print("🚀 第二次前向传播...")
        output2 = decoder(test_input, training=True)
        print(f"✅ 第二次调用成功，输出形状: {output2.shape}")
        
        # 检查输出范围
        print(f"输出范围: [{tf.reduce_min(output1):.3f}, {tf.reduce_max(output1):.3f}]")
        print(f"输出均值: {tf.reduce_mean(output1):.3f}")
        print(f"输出标准差: {tf.math.reduce_std(output1):.3f}")
        
        print("🎉 解码器测试通过！")
        
    except Exception as e:
        print(f"❌ 解码器测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_decoder_build():
    """测试解码器的build过程"""
    print("\n🔧 测试解码器build过程...")
    
    config = ImageJSCCConfig()
    decoder = ImageDecoder(config)
    
    # 检查各个组件是否正确创建
    print("检查解码器组件:")
    print(f"  feature_expansion: {type(decoder.feature_expansion)}")
    print(f"  transpose_conv: {type(decoder.transpose_conv)}")
    print(f"  cnn_upsampling_layers: {type(decoder.cnn_upsampling_layers)}")
    
    # 检查Sequential层的内容
    print("\nfeature_expansion层:")
    for i, layer in enumerate(decoder.feature_expansion.layers):
        print(f"  {i}: {layer.name} - {type(layer).__name__}")
    
    print("\ntranspose_conv层:")
    for i, layer in enumerate(decoder.transpose_conv.layers):
        print(f"  {i}: {layer.name} - {type(layer).__name__}")
    
    print("\ncnn_upsampling_layers层:")
    for i, layer in enumerate(decoder.cnn_upsampling_layers.layers):
        print(f"  {i}: {layer.name} - {type(layer).__name__}")

if __name__ == "__main__":
    print("=" * 60)
    print("ImageDecoder调试测试")
    print("=" * 60)
    
    # 测试build过程
    test_decoder_build()
    
    # 测试前向传播
    success = test_decoder()
    
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 测试失败，需要修复问题")

#!/usr/bin/env python3
"""
调试导入问题
"""

import sys
import os
import importlib

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_module_location():
    """检查模块的实际位置"""
    print("🔍 检查模块导入位置...")
    
    try:
        # 导入模块
        from layers.image_decoder import ImageDecoder
        
        # 检查模块文件位置
        module = sys.modules['layers.image_decoder']
        print(f"ImageDecoder模块位置: {module.__file__}")
        
        # 检查类的定义位置
        print(f"ImageDecoder类定义: {ImageDecoder}")
        
        # 检查call方法的源码位置
        import inspect
        call_method = getattr(ImageDecoder, 'call')
        print(f"call方法位置: {inspect.getfile(call_method)}")
        print(f"call方法行号: {inspect.getsourcelines(call_method)[1]}")
        
        # 获取call方法的源码
        source = inspect.getsource(call_method)
        lines = source.split('\n')
        print(f"call方法源码长度: {len(lines)} 行")

        # 检查是否有Dense(2048)的调用
        if 'Dense(2048' in source:
            print("⚠️  发现Dense(2048)调用！")
            for i, line in enumerate(lines):
                if 'Dense(2048' in line:
                    print(f"  第{i+1}行: {line.strip()}")
        else:
            print("✅ 没有发现Dense(2048)调用")
            
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()

def check_cached_modules():
    """检查缓存的模块"""
    print("\n🔍 检查已缓存的模块...")

    relevant_modules = [name for name in sys.modules.keys()
                       if 'image_decoder' in name.lower() or 'decoder' in name.lower()]

    for module_name in relevant_modules:
        module = sys.modules[module_name]
        if hasattr(module, '__file__') and module.__file__:
            print(f"  {module_name}: {module.__file__}")

def reload_modules():
    """重新加载模块"""
    print("\n🔄 重新加载模块...")

    # 删除相关模块的缓存
    modules_to_remove = []
    for name in sys.modules.keys():
        if any(part in name for part in ['layers', 'image_decoder', 'model']):
            modules_to_remove.append(name)

    for name in modules_to_remove:
        if name in sys.modules:
            print(f"  删除缓存: {name}")
            del sys.modules[name]

    # 重新导入
    try:
        from layers.image_decoder import ImageDecoder
        print("✅ 重新导入成功")
        return True
    except Exception as e:
        print(f"❌ 重新导入失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("模块导入调试")
    print("=" * 60)
    
    check_module_location()
    check_cached_modules()
    
    # 尝试重新加载
    if reload_modules():
        print("\n重新加载后:")
        check_module_location()

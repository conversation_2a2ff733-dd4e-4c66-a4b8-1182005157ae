import tensorflow as tf
from tensorflow.keras import layers
import numpy as np
from .attention_modules import MultiHeadAttention

class ImageDecoder(tf.keras.layers.Layer):
    def __init__(self, config, **kwargs):
        super(ImageDecoder, self).__init__(**kwargs)
        
        self.config = config
        self.embedding_dim = config.embedding_dim
        self.num_heads = config.TF_heads
        self.num_layers = config.dec_TF_layers
        self.compression_ratio = config.compression_ratio
        
        # 图像参数
        self.image_height = config.image_height
        self.image_width = config.image_width
        self.image_channels = config.image_channels
        
        # 压缩特征参数
        self.compressed_height = config.image_height // 8  # 32
        self.compressed_width = config.image_width // 8    # 64
        self.feature_dim = self.compressed_height * self.compressed_width  # 2048
        self.compressed_features = self.feature_dim // self.compression_ratio
        
        # 特征重建层
        self.feature_reconstruction = self._build_feature_reconstruction()

        # Transformer解码器层
        self.transformer_layers = self._build_transformer_layers()

        # 新的特征扩展层 - 预先创建所有Dense层
        self.feature_expansion = self._build_feature_expansion()

        # 转置卷积层
        self.transpose_conv = self._build_transpose_conv()

        # CNN上采样层 - 预先创建所有上采样层
        self.cnn_upsampling_layers = self._build_cnn_upsampling_layers()

        # CNN重建层
        self.conv_reconstruction = self._build_conv_reconstruction()

    def build(self, input_shape):
        """构建层 - 确保所有子层都被正确构建"""
        super(ImageDecoder, self).build(input_shape)

        # 确保所有Sequential层都被构建
        dummy_input = tf.zeros([1, self.compressed_features])

        # 构建特征扩展层
        _ = self.feature_expansion(dummy_input)

        # 构建转置卷积层的输入
        expanded_features = self.feature_expansion(dummy_input)
        reshaped_features = tf.reshape(expanded_features, [1, 16, 32, 32])
        _ = self.transpose_conv(reshaped_features)

        # 构建CNN上采样层
        transpose_output = self.transpose_conv(reshaped_features)
        _ = self.cnn_upsampling_layers(transpose_output)

    def _build_feature_reconstruction(self):
        """构建特征重建层"""
        reconstruction_layers = tf.keras.Sequential([
            layers.Dense(self.embedding_dim // 2, activation='relu'),
            layers.Dropout(0.1),
            layers.Dense(self.embedding_dim, activation='relu'),
            layers.Dropout(0.1),
            layers.Dense(self.feature_dim * self.embedding_dim, activation='relu'),
            layers.Reshape([self.feature_dim, self.embedding_dim])
        ])
        return reconstruction_layers
    
    def _build_transformer_layers(self):
        """构建Transformer解码器层"""
        transformer_layers = []
        for i in range(self.num_layers):
            # 多头注意力层
            attention_layer = MultiHeadAttention(
                head_num=self.num_heads,
                name=f'decoder_attention_{i}'
            )
            
            # 前馈网络
            ffn_layer = tf.keras.Sequential([
                layers.Dense(self.embedding_dim * 4, activation='relu'),
                layers.Dropout(0.1),
                layers.Dense(self.embedding_dim),
                layers.Dropout(0.1)
            ], name=f'decoder_ffn_{i}')
            
            # 层归一化
            norm1 = layers.LayerNormalization(name=f'decoder_norm1_{i}')
            norm2 = layers.LayerNormalization(name=f'decoder_norm2_{i}')
            
            transformer_layers.append({
                'attention': attention_layer,
                'ffn': ffn_layer,
                'norm1': norm1,
                'norm2': norm2
            })
        
        return transformer_layers
    
    def _build_conv_reconstruction(self):
        """构建CNN重建层 - 简化版本"""
        conv_layers = tf.keras.Sequential([
            # 直接从压缩特征重建到图像
            # 第一步：扩展到合适的特征维度
            layers.Dense(self.compressed_height * self.compressed_width * 128, activation='relu'),
            layers.Reshape([self.compressed_height, self.compressed_width, 128]),

            # 第一个上采样块 (32x64 -> 64x128)
            layers.Conv2DTranspose(64, 4, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),

            # 第二个上采样块 (64x128 -> 128x256)
            layers.Conv2DTranspose(32, 4, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),

            # 第三个上采样块 (128x256 -> 256x512)
            layers.Conv2DTranspose(16, 4, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),

            # 最终输出层 - 直接输出到[0,1]
            layers.Conv2D(self.image_channels, 3, strides=1, padding='same', activation='sigmoid'),
        ])
        return conv_layers

    def _build_feature_expansion(self):
        """构建特征扩展层 - 预先创建所有Dense层"""
        expansion_layers = tf.keras.Sequential([
            # 从2048维开始逐步扩展
            layers.Dense(4096, activation='relu', name='expand_1'),
            layers.Dropout(0.1),

            layers.Dense(8192, activation='relu', name='expand_2'),
            layers.Dropout(0.1),

            # 扩展到更大的中间维度
            layers.Dense(16384, activation='relu', name='expand_3'),
            layers.Dropout(0.1),
        ], name='feature_expansion')
        return expansion_layers

    def _build_transpose_conv(self):
        """构建转置卷积层"""
        transpose_layers = tf.keras.Sequential([
            # 16x32 -> 32x64 (目标压缩尺寸)
            layers.Conv2DTranspose(self.embedding_dim, 4, strides=2, padding='same',
                                 activation='relu', name='transpose_conv_1'),
            layers.BatchNormalization(name='transpose_bn_1'),
        ], name='transpose_conv')
        return transpose_layers

    def _build_cnn_upsampling_layers(self):
        """构建CNN上采样层 - 预先创建所有层"""
        upsampling_layers = tf.keras.Sequential([
            # 第一个上采样块 (32x64 -> 64x128)
            layers.Conv2DTranspose(128, 4, strides=2, padding='same',
                                 activation='relu', name='upsample_conv1'),
            layers.BatchNormalization(name='upsample_bn1'),

            # 第二个上采样块 (64x128 -> 128x256)
            layers.Conv2DTranspose(64, 4, strides=2, padding='same',
                                 activation='relu', name='upsample_conv2'),
            layers.BatchNormalization(name='upsample_bn2'),

            # 第三个上采样块 (128x256 -> 256x512)
            layers.Conv2DTranspose(32, 4, strides=2, padding='same',
                                 activation='relu', name='upsample_conv3'),
            layers.BatchNormalization(name='upsample_bn3'),

            # 最终输出层
            layers.Conv2D(self.image_channels, 3, strides=1, padding='same',
                         activation='sigmoid', name='final_output'),
        ], name='cnn_upsampling')
        return upsampling_layers

    def _positional_encoding(self, seq_len, d_model):
        """生成正弦位置编码"""
        position = np.arange(seq_len)[:, np.newaxis]
        div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))
        
        pos_encoding = np.zeros((seq_len, d_model))
        pos_encoding[:, 0::2] = np.sin(position * div_term)
        pos_encoding[:, 1::2] = np.cos(position * div_term)
        
        return tf.constant(pos_encoding, dtype=tf.float32)

    def _cnn_upsampling(self, x, training=False):
        """CNN上采样重建 - 使用预先创建的层"""
        # x shape: [batch, compressed_height, compressed_width, embedding_dim]

        # 使用预先创建的上采样层
        x = self.cnn_upsampling_layers(x, training=training)

        return x

    def call(self, inputs, training=False):
        """前向传播 - 使用预先创建的层"""
        # inputs shape: [batch, compressed_features] (现在是2048维)
        batch_size = tf.shape(inputs)[0]

        # 第一步：使用预先创建的特征扩展层
        x = self.feature_expansion(inputs, training=training)

        # 重塑为更大的特征图，保留更多空间信息
        # 重塑为 16x32x32 的特征图 (16*32*32 = 16384)
        x = tf.reshape(x, [batch_size, 16, 32, 32])

        # 第二步：使用预先创建的转置卷积层
        x = self.transpose_conv(x, training=training)
        # x shape: [batch, 32, 64, embedding_dim]

        # 第三步：CNN上采样重建到原始图像尺寸
        reconstructed_image = self._cnn_upsampling(x, training=training)
        # reconstructed_image shape: [batch, height, width, channels]

        return reconstructed_image

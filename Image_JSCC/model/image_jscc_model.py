import tensorflow as tf
from tensorflow.keras import layers
import os
import sys

# 添加路径以导入自定义模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from layers.image_encoder import ImageEncoder
from layers.image_decoder import ImageDecoder
from layers.quantization import QuantizationLayer, DequantizationLayer
from channel.channel import create_channel

class ImageJSCCModel(tf.keras.Model):
    """图像联合信源信道编码模型"""
    
    def __init__(self, config, **kwargs):
        super(ImageJSCCModel, self).__init__(**kwargs)
        
        self.config = config
        self.quantization_bits = config.quantization_bits
        self.compression_ratio = config.compression_ratio
        
        # 计算压缩特征维度
        self.original_features = (config.image_height // 8) * (config.image_width // 8) // config.compression_ratio
        
        # 编码器
        self.encoder = ImageEncoder(config, name='image_encoder')
        
        # 简化量化层 - 使用简单的线性变换
        self.quantization = tf.keras.Sequential([
            tf.keras.layers.Dense(self.original_features * config.quantization_bits,
                                activation='sigmoid', name='quantization_dense')
        ], name='quantization')
        
        # 信道层
        if config.channel_type.upper() == "OFDM":
            self.channel = create_channel(
                channel_type=config.channel_type,
                num_subcarriers=getattr(config, 'num_subcarriers', 64),
                cp_length=getattr(config, 'cp_length', 16)
            )
        else:
            self.channel = create_channel(channel_type=config.channel_type)
        
        # 简化反量化层 - 使用简单的线性变换
        self.dequantization = tf.keras.Sequential([
            tf.keras.layers.Dense(self.original_features,
                                activation='sigmoid', name='dequantization_dense')
        ], name='dequantization')
        
        # 解码器
        self.decoder = ImageDecoder(config, name='image_decoder')
        
    def call(self, inputs, snr_db=None, training=False):
        """
        前向传播
        
        Args:
            inputs: [batch, height, width, channels] float32, 输入图像
            snr_db: float, 信噪比 (dB)
            training: bool, 是否为训练模式
        
        Returns:
            reconstructed: [batch, height, width, channels] float32, 重建图像
        """
        if snr_db is None:
            snr_db = self.config.snr_dB
        
        # 编码
        encoded_features = self.encoder(inputs, training=training)
        # encoded_features shape: [batch, compressed_features]

        # 跳过量化，直接传输float特征
        # 量化 (注释掉)
        # quantized_bits = self.quantization(encoded_features, training=training)
        # quantized_bits shape: [batch, compressed_features * quantization_bits]

        # 信道传输 (直接传输float特征)
        received_features = self.channel(encoded_features, snr_db=snr_db, training=training)
        # received_features shape: [batch, compressed_features]

        # 反量化 (注释掉)
        # dequantized_features = self.dequantization(received_bits, training=training)
        # dequantized_features shape: [batch, compressed_features]

        # 解码 (直接使用接收到的float特征)
        reconstructed = self.decoder(received_features, training=training)
        # reconstructed shape: [batch, height, width, channels]
        
        return reconstructed
    
    def encode(self, inputs, training=False):
        """仅编码，返回压缩特征"""
        return self.encoder(inputs, training=training)
    
    def decode(self, features, training=False):
        """仅解码，从压缩特征重建图像"""
        return self.decoder(features, training=training)
    
    def get_compression_ratio(self):
        """计算实际压缩比"""
        input_size = self.config.image_height * self.config.image_width * self.config.image_channels * 8  # bits
        compressed_size = self.original_features * self.quantization_bits  # bits
        return input_size / compressed_size
    
    def save_weights_custom(self, filepath, save_format='tf'):
        """自定义权重保存 - 确保文件名格式正确"""
        # 确保文件名以.weights.h5结尾
        if not filepath.endswith('.weights.h5'):
            if filepath.endswith('.h5'):
                # 如果已经有.h5，替换为.weights.h5
                filepath = filepath[:-3] + '.weights.h5'
            else:
                # 如果没有扩展名，添加.weights.h5
                filepath = filepath + '.weights.h5'

        if save_format == 'tf':
            self.save_weights(filepath)
        else:
            # 其他格式也使用相同的文件名规则
            self.save_weights(filepath)
    
    def load_weights_custom(self, filepath):
        """自定义权重加载 - 自动处理文件名格式"""
        # 如果文件不存在，尝试添加.weights.h5扩展名
        import os
        if not os.path.exists(filepath):
            if not filepath.endswith('.weights.h5'):
                if filepath.endswith('.h5'):
                    # 如果已经有.h5，替换为.weights.h5
                    filepath = filepath[:-3] + '.weights.h5'
                else:
                    # 如果没有扩展名，添加.weights.h5
                    filepath = filepath + '.weights.h5'

        self.load_weights(filepath)
    
    def summary_custom(self):
        """自定义模型摘要"""
        print("=" * 60)
        print("Image JSCC Model Summary")
        print("=" * 60)
        print(f"Input shape: ({self.config.image_height}, {self.config.image_width}, {self.config.image_channels})")
        print(f"Compression ratio: {self.compression_ratio}")
        print(f"Quantization bits: {self.quantization_bits}")
        print(f"Channel type: {self.config.channel_type}")
        print(f"SNR: {self.config.snr_dB} dB")
        print(f"Compressed features: {self.original_features}")
        print(f"Transmitted bits: {self.original_features * self.quantization_bits}")
        print(f"Actual compression ratio: {self.get_compression_ratio():.2f}")
        print("=" * 60)

        # 确保模型已经构建
        if not self.built:
            print("⚠️  模型尚未构建，正在构建...")
            dummy_input = tf.random.normal([1, self.config.image_height, self.config.image_width, self.config.image_channels])
            _ = self(dummy_input, training=False)

        # 打印各组件参数数量
        total_params = 0
        for component_name, component in [
            ('Encoder', self.encoder),
            ('Decoder', self.decoder)
        ]:
            try:
                if hasattr(component, 'trainable_variables') and component.trainable_variables:
                    component_params = sum([tf.size(var).numpy() for var in component.trainable_variables])
                else:
                    # 如果组件没有trainable_variables，尝试构建它
                    print(f"⚠️  {component_name} 没有可训练参数，尝试构建...")
                    if component_name == 'Decoder':
                        # 对解码器进行一次前向传播以构建参数
                        dummy_features = tf.random.normal([1, self.original_features])
                        _ = component(dummy_features, training=False)
                        component_params = sum([tf.size(var).numpy() for var in component.trainable_variables])
                    else:
                        component_params = 0

                total_params += component_params
                print(f"{component_name} parameters: {component_params:,}")
            except Exception as e:
                print(f"❌ 无法计算{component_name}参数: {e}")
                print(f"{component_name} parameters: 未知")

        print(f"Total trainable parameters: {total_params:,}")
        print("=" * 60)

def create_model(config):
    """创建模型的工厂函数"""
    model = ImageJSCCModel(config)
    
    # 构建模型 (通过一次前向传播)
    dummy_input = tf.random.normal([
        1, 
        config.image_height, 
        config.image_width, 
        config.image_channels
    ])
    _ = model(dummy_input, training=False)
    
    return model

# 测试函数
def test_model():
    """测试模型"""
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from config import ImageJSCCConfig
    
    config = ImageJSCCConfig()
    model = create_model(config)
    
    # 创建测试输入
    test_input = tf.random.uniform([2, config.image_height, config.image_width, config.image_channels])
    
    print("Testing model...")
    print(f"Input shape: {test_input.shape}")
    
    # 前向传播
    output = model(test_input, snr_db=config.snr_dB, training=True)
    print(f"Output shape: {output.shape}")
    print(f"Output range: [{tf.reduce_min(output):.3f}, {tf.reduce_max(output):.3f}]")
    
    # 显示模型摘要
    model.summary_custom()
    
    # 测试编码和解码
    encoded = model.encode(test_input)
    decoded = model.decode(encoded)
    print(f"Encoded shape: {encoded.shape}")
    print(f"Decoded shape: {decoded.shape}")

if __name__ == "__main__":
    test_model()

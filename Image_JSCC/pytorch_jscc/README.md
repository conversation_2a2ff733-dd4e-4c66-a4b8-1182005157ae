# PyTorch Image JSCC

一个简洁高效的PyTorch图像联合信源信道编码(JSCC)实现，解决了原TensorFlow版本的亮斑点问题。

## 🚀 特性

- **简洁架构**: CNN编码器 + 转置卷积解码器
- **稳定训练**: 解决了亮斑点问题，使用合适的权重初始化和激活函数
- **合理压缩比**: 24.6x压缩比，平衡质量和压缩率
- **多种损失函数**: MSE + SSIM + 可选感知损失
- **完整工具链**: 训练、测试、可视化一体化

## 📊 模型架构

### 编码器
- CNN特征提取: 256×512×3 → 16×32×512
- 全局平均池化: 16×32×512 → 512
- 全连接压缩: 512 → 512 (latent_dim)

### 解码器  
- 特征扩展: 512 → 16×32×256
- 转置卷积重建: 16×32×256 → 256×512×3

### 压缩比分析
- 原始图像: 3,145,728 bits
- 压缩特征: 4,096 bits (512×8bits)
- 压缩比: **24.6x**

## 🛠️ 安装依赖

```bash
pip install torch torchvision matplotlib tensorboard tqdm pillow numpy
```

## 📁 数据准备

将Cityscapes数据集放在以下路径:
```
/home/<USER>/GESCO/data/Cityscapes/leftImg8bit/
├── train/
└── val/
```

## 🏋️ 训练

### 基础训练
```bash
cd pytorch_jscc
python train.py
```

### 自定义参数
```bash
python train.py --epochs 200 --lr 5e-5
```

### 恢复训练
```bash
python train.py --resume checkpoints/checkpoint_epoch_50.pth
```

## 🧪 测试

### 测试数据集
```bash
python test.py --checkpoint checkpoints/best_model.pth
```

### 测试单张图像
```bash
python test.py --checkpoint checkpoints/best_model.pth --image path/to/image.jpg
```

## 📈 监控训练

启动TensorBoard:
```bash
tensorboard --logdir logs
```

## 🔧 配置参数

在 `config.py` 中修改:

```python
class ImageJSCCConfig:
    # 图像参数
    image_height = 256
    image_width = 512
    
    # 模型参数
    latent_dim = 512        # 压缩特征维度
    quantization_bits = 8   # 量化比特数
    
    # 训练参数
    batch_size = 8
    learning_rate = 1e-4
    epochs = 100
    
    # 信道参数
    snr_db = 10.0
    channel_type = "awgn"
```

## 📊 预期结果

经过100个epoch的训练，预期指标:
- **PSNR**: 25-30 dB
- **SSIM**: 0.85-0.92
- **压缩比**: 24.6x

## 🔍 主要改进

相比原TensorFlow版本:

1. **解决亮斑点问题**:
   - 使用合适的权重初始化 (Kaiming/Xavier)
   - 替换ReLU为LeakyReLU，避免死神经元
   - 降低学习率，防止梯度爆炸

2. **优化架构**:
   - 简化编码器，使用全局池化
   - 改进解码器，使用批归一化
   - 合理的压缩比设计

3. **稳定训练**:
   - 梯度裁剪
   - 学习率调度
   - 组合损失函数

## 📝 文件结构

```
pytorch_jscc/
├── config.py          # 配置文件
├── models.py          # 模型定义
├── dataset.py         # 数据加载
├── losses.py          # 损失函数
├── train.py           # 训练脚本
├── test.py            # 测试脚本
├── README.md          # 说明文档
├── checkpoints/       # 模型检查点
├── logs/              # TensorBoard日志
└── samples/           # 训练样本图像
```

## 🎯 使用建议

1. **首次训练**: 使用默认参数开始
2. **调优**: 根据结果调整 `latent_dim` 和学习率
3. **监控**: 观察PSNR和SSIM的变化趋势
4. **早停**: 如果验证指标不再改善，可以提前停止

## 🐛 故障排除

### 内存不足
- 减小 `batch_size`
- 降低 `latent_dim`

### 训练不稳定
- 降低学习率
- 增加梯度裁剪

### 重建质量差
- 增加 `latent_dim`
- 调整损失函数权重
- 增加训练epoch数

## 📞 支持

如有问题，请检查:
1. 数据路径是否正确
2. GPU内存是否充足
3. 依赖包是否安装完整

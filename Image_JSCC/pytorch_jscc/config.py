"""
PyTorch Image JSCC Configuration
"""

class ImageJSCCConfig:
    def __init__(self):
        # 图像参数
        self.image_height = 256
        self.image_width = 512
        self.image_channels = 3
        
        # 训练参数
        self.batch_size = 8
        self.epochs = 100
        self.learning_rate = 1e-4
        self.weight_decay = 1e-5
        
        # 模型参数 - 优化为最佳压缩比
        self.latent_dim = 4096  # 压缩特征维度 (96x压缩比，平衡质量和效率)
        self.quantization_bits = 8
        
        # 信道参数
        self.snr_db = 10.0
        self.channel_type = "awgn"  # "awgn", "rayleigh"
        
        # 数据路径
        self.train_data_path = "/home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train"
        self.val_data_path = "/home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val"
        
        # 保存路径
        self.checkpoint_dir = "./checkpoints"
        self.log_dir = "./logs"
        
        # 计算压缩比
        original_bits = self.image_height * self.image_width * self.image_channels * 8
        compressed_bits = self.latent_dim * self.quantization_bits
        self.compression_ratio = original_bits / compressed_bits
        
        print(f"压缩比: {self.compression_ratio:.1f}x")
        print(f"原始: {original_bits:,} bits -> 压缩: {compressed_bits:,} bits")

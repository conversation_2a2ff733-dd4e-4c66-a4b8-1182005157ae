"""
PyTorch Dataset for Image JSCC
"""

import os
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from PIL import Image
import glob

class CityscapesDataset(Dataset):
    """Cityscapes数据集"""
    
    def __init__(self, data_path, image_size=(256, 512), transform=None):
        self.data_path = data_path
        self.image_size = image_size
        
        # 查找所有图像文件
        self.image_paths = []
        for root, dirs, files in os.walk(data_path):
            for file in files:
                if file.endswith(('.png', '.jpg', '.jpeg')):
                    self.image_paths.append(os.path.join(root, file))
        
        print(f"Found {len(self.image_paths)} images in {data_path}")
        
        # 默认变换
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize(image_size),
                transforms.ToTensor(),  # 转换到[0,1]
            ])
        else:
            self.transform = transform
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        
        try:
            # 加载图像
            image = Image.open(image_path).convert('RGB')
            
            # 应用变换
            if self.transform:
                image = self.transform(image)
            
            return image
        
        except Exception as e:
            print(f"Error loading image {image_path}: {e}")
            # 返回随机图像作为备用
            return torch.rand(3, *self.image_size)

def create_dataloaders(config):
    """创建训练和验证数据加载器"""
    
    # 数据增强
    train_transform = transforms.Compose([
        transforms.Resize((config.image_height, config.image_width)),
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.05),
        transforms.ToTensor(),
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((config.image_height, config.image_width)),
        transforms.ToTensor(),
    ])
    
    # 创建数据集
    train_dataset = CityscapesDataset(
        config.train_data_path, 
        (config.image_height, config.image_width),
        train_transform
    )
    
    val_dataset = CityscapesDataset(
        config.val_data_path,
        (config.image_height, config.image_width), 
        val_transform
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True,
        drop_last=False
    )
    
    return train_loader, val_loader

def test_dataset():
    """测试数据集"""
    from config import ImageJSCCConfig
    
    config = ImageJSCCConfig()
    train_loader, val_loader = create_dataloaders(config)
    
    print(f"Train batches: {len(train_loader)}")
    print(f"Val batches: {len(val_loader)}")
    
    # 测试一个批次
    for batch in train_loader:
        print(f"Batch shape: {batch.shape}")
        print(f"Batch range: [{batch.min():.3f}, {batch.max():.3f}]")
        break

if __name__ == "__main__":
    test_dataset()

"""
Loss Functions for Image JSCC
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.models import vgg16
import numpy as np

class CombinedLoss(nn.Module):
    """组合损失函数"""
    
    def __init__(self, mse_weight=1.0, ssim_weight=0.5, perceptual_weight=0.1):
        super(CombinedLoss, self).__init__()
        self.mse_weight = mse_weight
        self.ssim_weight = ssim_weight
        self.perceptual_weight = perceptual_weight
        
        self.mse_loss = nn.MSELoss()
        self.ssim_loss = SSIMLoss()
        
        if perceptual_weight > 0:
            self.perceptual_loss = PerceptualLoss()
    
    def forward(self, pred, target):
        # MSE损失
        mse = self.mse_loss(pred, target)
        
        # SSIM损失
        ssim = self.ssim_loss(pred, target)
        
        # 总损失
        total_loss = self.mse_weight * mse + self.ssim_weight * ssim
        
        # 感知损失（可选）
        if self.perceptual_weight > 0:
            perceptual = self.perceptual_loss(pred, target)
            total_loss += self.perceptual_weight * perceptual
        
        return total_loss, {
            'mse': mse.item(),
            'ssim': ssim.item(),
            'total': total_loss.item()
        }

class SSIMLoss(nn.Module):
    """SSIM损失"""
    
    def __init__(self, window_size=11, size_average=True):
        super(SSIMLoss, self).__init__()
        self.window_size = window_size
        self.size_average = size_average
        self.channel = 1
        self.window = self.create_window(window_size, self.channel)
    
    def gaussian(self, window_size, sigma):
        gauss = torch.Tensor([np.exp(-(x - window_size//2)**2/float(2*sigma**2)) for x in range(window_size)])
        return gauss/gauss.sum()
    
    def create_window(self, window_size, channel):
        _1D_window = self.gaussian(window_size, 1.5).unsqueeze(1)
        _2D_window = _1D_window.mm(_1D_window.t()).float().unsqueeze(0).unsqueeze(0)
        window = _2D_window.expand(channel, 1, window_size, window_size).contiguous()
        return window
    
    def _ssim(self, img1, img2, window, window_size, channel, size_average=True):
        mu1 = F.conv2d(img1, window, padding=window_size//2, groups=channel)
        mu2 = F.conv2d(img2, window, padding=window_size//2, groups=channel)
        
        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1*mu2
        
        sigma1_sq = F.conv2d(img1*img1, window, padding=window_size//2, groups=channel) - mu1_sq
        sigma2_sq = F.conv2d(img2*img2, window, padding=window_size//2, groups=channel) - mu2_sq
        sigma12 = F.conv2d(img1*img2, window, padding=window_size//2, groups=channel) - mu1_mu2
        
        C1 = 0.01**2
        C2 = 0.03**2
        
        ssim_map = ((2*mu1_mu2 + C1)*(2*sigma12 + C2))/((mu1_sq + mu2_sq + C1)*(sigma1_sq + sigma2_sq + C2))
        
        if size_average:
            return ssim_map.mean()
        else:
            return ssim_map.mean(1).mean(1).mean(1)
    
    def forward(self, img1, img2):
        (_, channel, _, _) = img1.size()
        
        if channel == self.channel and self.window.data.type() == img1.data.type():
            window = self.window
        else:
            window = self.create_window(self.window_size, channel)
            
            if img1.is_cuda:
                window = window.cuda(img1.get_device())
            window = window.type_as(img1)
            
            self.window = window
            self.channel = channel
        
        ssim_value = self._ssim(img1, img2, window, self.window_size, channel, self.size_average)
        return 1 - ssim_value  # 转换为损失

class PerceptualLoss(nn.Module):
    """感知损失（基于VGG特征）"""
    
    def __init__(self):
        super(PerceptualLoss, self).__init__()
        vgg = vgg16(pretrained=True).features
        self.feature_extractor = nn.Sequential(*list(vgg.children())[:16])
        
        # 冻结参数
        for param in self.feature_extractor.parameters():
            param.requires_grad = False
        
        self.mse_loss = nn.MSELoss()
    
    def forward(self, pred, target):
        # 提取特征
        pred_features = self.feature_extractor(pred)
        target_features = self.feature_extractor(target)
        
        # 计算特征损失
        return self.mse_loss(pred_features, target_features)

def calculate_psnr(pred, target, max_val=1.0):
    """计算PSNR"""
    mse = torch.mean((pred - target) ** 2)
    if mse == 0:
        return float('inf')
    return 20 * torch.log10(max_val / torch.sqrt(mse))

def calculate_ssim(pred, target):
    """计算SSIM值（不是损失）"""
    ssim_loss = SSIMLoss()
    return 1 - ssim_loss(pred, target)

class MetricsCalculator:
    """指标计算器"""
    
    def __init__(self):
        self.ssim_loss = SSIMLoss()
    
    def __call__(self, pred, target):
        with torch.no_grad():
            # MSE
            mse = torch.mean((pred - target) ** 2)
            
            # PSNR
            psnr = calculate_psnr(pred, target)
            
            # SSIM
            ssim = calculate_ssim(pred, target)
            
            # MAE
            mae = torch.mean(torch.abs(pred - target))
            
            return {
                'mse': mse.item(),
                'psnr': psnr.item(),
                'ssim': ssim.item(),
                'mae': mae.item()
            }

"""
PyTorch Image JSCC Models
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class ImageEncoder(nn.Module):
    """图像编码器 - CNN + 全局池化"""
    
    def __init__(self, config):
        super(ImageEncoder, self).__init__()
        self.config = config
        
        # CNN特征提取
        self.conv_layers = nn.Sequential(
            # Block 1: 256x512 -> 128x256
            nn.Conv2d(3, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, 3, stride=2, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            
            # Block 2: 128x256 -> 64x128
            nn.Conv2d(64, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, 3, stride=2, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            
            # Block 3: 64x128 -> 32x64
            nn.Conv2d(128, 256, 3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, stride=2, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            
            # Block 4: 32x64 -> 16x32
            nn.Conv2d(256, 512, 3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, 3, stride=2, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
        )
        
        # 全局平均池化 + 压缩
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        self.compression = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(256, config.latent_dim),
            nn.Tanh()  # 输出到[-1,1]范围
        )
        
        # 权重初始化
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # CNN特征提取
        features = self.conv_layers(x)  # [B, 512, 16, 32]
        
        # 全局池化
        pooled = self.global_pool(features)  # [B, 512, 1, 1]
        pooled = pooled.view(pooled.size(0), -1)  # [B, 512]
        
        # 压缩
        compressed = self.compression(pooled)  # [B, latent_dim]
        
        return compressed

class ImageDecoder(nn.Module):
    """图像解码器 - 转置卷积重建"""
    
    def __init__(self, config):
        super(ImageDecoder, self).__init__()
        self.config = config
        
        # 特征扩展 - 适应更大的latent_dim
        self.feature_expansion = nn.Sequential(
            nn.Linear(config.latent_dim, 1024),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(1024, 512),
            nn.ReLU(inplace=True),
            nn.Linear(512, 16 * 32 * 256),  # 16x32x256
            nn.ReLU(inplace=True)
        )
        
        # 转置卷积重建
        self.decoder_layers = nn.Sequential(
            # 16x32 -> 32x64
            nn.ConvTranspose2d(256, 256, 4, stride=2, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            
            # 32x64 -> 64x128
            nn.ConvTranspose2d(256, 128, 4, stride=2, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            
            # 64x128 -> 128x256
            nn.ConvTranspose2d(128, 64, 4, stride=2, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            
            # 128x256 -> 256x512
            nn.ConvTranspose2d(64, 32, 4, stride=2, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            
            # 最终输出层
            nn.Conv2d(32, 3, 3, padding=1),
            nn.Sigmoid()  # 输出到[0,1]
        )
        
        # 权重初始化
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, (nn.ConvTranspose2d, nn.Conv2d)):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # 特征扩展
        expanded = self.feature_expansion(x)  # [B, 16*32*256]
        
        # 重塑为特征图
        batch_size = x.size(0)
        reshaped = expanded.view(batch_size, 256, 16, 32)  # [B, 256, 16, 32]
        
        # 转置卷积重建
        reconstructed = self.decoder_layers(reshaped)  # [B, 3, 256, 512]
        
        return reconstructed

class QuantizationLayer(nn.Module):
    """量化层"""
    
    def __init__(self, bits=8):
        super(QuantizationLayer, self).__init__()
        self.bits = bits
        self.levels = 2 ** bits
    
    def forward(self, x):
        if self.training:
            # 训练时使用噪声量化
            noise = torch.rand_like(x) - 0.5
            x_q = torch.round(x * (self.levels - 1) + noise) / (self.levels - 1)
        else:
            # 推理时使用标准量化
            x_q = torch.round(x * (self.levels - 1)) / (self.levels - 1)
        
        return x_q

class ChannelLayer(nn.Module):
    """信道层"""
    
    def __init__(self, channel_type="awgn"):
        super(ChannelLayer, self).__init__()
        self.channel_type = channel_type
    
    def forward(self, x, snr_db=10.0):
        if not self.training:
            return x
        
        # 计算信号功率
        signal_power = torch.mean(x ** 2)
        
        # 计算噪声功率
        snr_linear = 10 ** (snr_db / 10.0)
        noise_power = signal_power / snr_linear
        
        if self.channel_type == "awgn":
            # AWGN信道
            noise = torch.randn_like(x) * torch.sqrt(noise_power)
            return x + noise
        
        elif self.channel_type == "rayleigh":
            # 瑞利衰落信道
            h = torch.randn_like(x) * 0.7071 + 1j * torch.randn_like(x) * 0.7071
            h_magnitude = torch.abs(h)
            faded = x * h_magnitude
            
            noise = torch.randn_like(x) * torch.sqrt(noise_power)
            received = faded + noise
            
            # 理想信道估计
            return received / (h_magnitude + 1e-8)
        
        else:
            return x

class ImageJSCC(nn.Module):
    """完整的图像JSCC模型"""
    
    def __init__(self, config):
        super(ImageJSCC, self).__init__()
        self.config = config
        
        self.encoder = ImageEncoder(config)
        self.quantizer = QuantizationLayer(config.quantization_bits)
        self.channel = ChannelLayer(config.channel_type)
        self.decoder = ImageDecoder(config)
    
    def forward(self, x, snr_db=None, training=None):
        if snr_db is None:
            snr_db = self.config.snr_db
        
        if training is None:
            training = self.training
        
        # 编码
        encoded = self.encoder(x)
        
        # 量化 (可选)
        # quantized = self.quantizer(encoded)
        
        # 信道传输
        received = self.channel(encoded, snr_db)
        
        # 解码
        reconstructed = self.decoder(received)
        
        return reconstructed
    
    def encode(self, x):
        """仅编码"""
        return self.encoder(x)
    
    def decode(self, z):
        """仅解码"""
        return self.decoder(z)

"""
Test Script for PyTorch Image JSCC
"""

import os
import torch
import matplotlib.pyplot as plt
import numpy as np
from PIL import Image
import argparse

from config import ImageJSCCConfig
from models import ImageJSCC
from dataset import create_dataloaders
from losses import MetricsCalculator

def test_model(model, test_loader, device, save_dir='test_results'):
    """测试模型"""
    model.eval()
    
    os.makedirs(save_dir, exist_ok=True)
    metrics_calc = MetricsCalculator()
    
    all_metrics = {'psnr': [], 'ssim': [], 'mse': [], 'mae': []}
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            batch = batch.to(device)
            
            # 重建
            reconstructed = model(batch, training=False)
            
            # 计算指标
            metrics = metrics_calc(reconstructed, batch)
            for key in all_metrics:
                if key in metrics:
                    all_metrics[key].append(metrics[key])
            
            # 保存前几个批次的图像
            if batch_idx < 5:
                save_batch_images(batch, reconstructed, batch_idx, save_dir)
    
    # 计算平均指标
    avg_metrics = {k: np.mean(v) for k, v in all_metrics.items()}
    
    print("Test Results:")
    print(f"PSNR: {avg_metrics['psnr']:.2f} ± {np.std(all_metrics['psnr']):.2f} dB")
    print(f"SSIM: {avg_metrics['ssim']:.4f} ± {np.std(all_metrics['ssim']):.4f}")
    print(f"MSE: {avg_metrics['mse']:.6f} ± {np.std(all_metrics['mse']):.6f}")
    print(f"MAE: {avg_metrics['mae']:.6f} ± {np.std(all_metrics['mae']):.6f}")
    
    return avg_metrics

def save_batch_images(original, reconstructed, batch_idx, save_dir):
    """保存一个批次的图像对比"""
    batch_size = min(4, original.size(0))
    
    fig, axes = plt.subplots(2, batch_size, figsize=(batch_size * 4, 8))
    if batch_size == 1:
        axes = axes.reshape(2, 1)
    
    for i in range(batch_size):
        # 原图
        orig_img = original[i].cpu().permute(1, 2, 0).numpy()
        orig_img = np.clip(orig_img, 0, 1)
        axes[0, i].imshow(orig_img)
        axes[0, i].set_title(f'Original {i+1}')
        axes[0, i].axis('off')
        
        # 重建图
        recon_img = reconstructed[i].cpu().permute(1, 2, 0).numpy()
        recon_img = np.clip(recon_img, 0, 1)
        axes[1, i].imshow(recon_img)
        axes[1, i].set_title(f'Reconstructed {i+1}')
        axes[1, i].axis('off')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/batch_{batch_idx:03d}_comparison.png', dpi=150, bbox_inches='tight')
    plt.close()

def test_single_image(model, image_path, device, save_path='single_test.png'):
    """测试单张图像"""
    from torchvision import transforms
    
    model.eval()
    
    # 加载和预处理图像
    transform = transforms.Compose([
        transforms.Resize((256, 512)),
        transforms.ToTensor(),
    ])
    
    image = Image.open(image_path).convert('RGB')
    input_tensor = transform(image).unsqueeze(0).to(device)
    
    with torch.no_grad():
        # 重建
        reconstructed = model(input_tensor, training=False)
        
        # 计算指标
        metrics_calc = MetricsCalculator()
        metrics = metrics_calc(reconstructed, input_tensor)
        
        # 可视化
        fig, axes = plt.subplots(1, 2, figsize=(12, 6))
        
        # 原图
        orig_img = input_tensor[0].cpu().permute(1, 2, 0).numpy()
        axes[0].imshow(orig_img)
        axes[0].set_title('Original')
        axes[0].axis('off')
        
        # 重建图
        recon_img = reconstructed[0].cpu().permute(1, 2, 0).numpy()
        recon_img = np.clip(recon_img, 0, 1)
        axes[1].imshow(recon_img)
        axes[1].set_title(f'Reconstructed\nPSNR: {metrics["psnr"]:.2f}dB, SSIM: {metrics["ssim"]:.4f}')
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.show()
        
        print(f"Single image test results:")
        print(f"PSNR: {metrics['psnr']:.2f} dB")
        print(f"SSIM: {metrics['ssim']:.4f}")

def analyze_compression_ratio(config):
    """分析压缩比"""
    print("Compression Analysis:")
    print(f"Input image: {config.image_height}×{config.image_width}×{config.image_channels}")
    print(f"Latent dimension: {config.latent_dim}")
    print(f"Quantization bits: {config.quantization_bits}")
    
    original_bits = config.image_height * config.image_width * config.image_channels * 8
    compressed_bits = config.latent_dim * config.quantization_bits
    compression_ratio = original_bits / compressed_bits
    
    print(f"Original size: {original_bits:,} bits")
    print(f"Compressed size: {compressed_bits:,} bits")
    print(f"Compression ratio: {compression_ratio:.1f}x")

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--checkpoint', type=str, required=True, help='Path to model checkpoint')
    parser.add_argument('--image', type=str, help='Test single image')
    parser.add_argument('--output_dir', type=str, default='test_results', help='Output directory')
    args = parser.parse_args()
    
    # 配置
    config = ImageJSCCConfig()
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # 加载模型
    print(f"Loading model from {args.checkpoint}")
    model = ImageJSCC(config).to(device)
    
    checkpoint = torch.load(args.checkpoint, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    print(f"Model loaded from epoch {checkpoint['epoch']}")
    if 'best_psnr' in checkpoint:
        print(f"Best PSNR during training: {checkpoint['best_psnr']:.2f} dB")
    
    # 分析压缩比
    analyze_compression_ratio(config)
    
    if args.image:
        # 测试单张图像
        test_single_image(model, args.image, device, f'{args.output_dir}/single_test.png')
    else:
        # 测试数据集
        print("Loading test dataset...")
        _, test_loader = create_dataloaders(config)
        
        print("Testing model...")
        test_model(model, test_loader, device, args.output_dir)

if __name__ == '__main__':
    main()

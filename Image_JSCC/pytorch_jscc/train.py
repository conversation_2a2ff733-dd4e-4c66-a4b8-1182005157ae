"""
Training Script for PyTorch Image JSCC
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import matplotlib.pyplot as plt
import numpy as np
from tqdm import tqdm
import argparse

from config import ImageJSCCConfig
from models import ImageJSCC
from dataset import create_dataloaders
from losses import CombinedLoss, MetricsCalculator

def save_sample_images(model, val_loader, epoch, save_dir, device, num_samples=4):
    """保存样本重建图像"""
    model.eval()
    
    with torch.no_grad():
        # 获取一个批次
        for batch in val_loader:
            batch = batch.to(device)
            break
        
        # 重建
        reconstructed = model(batch[:num_samples])
        
        # 创建对比图
        fig, axes = plt.subplots(2, num_samples, figsize=(num_samples * 4, 8))
        
        for i in range(num_samples):
            # 原图
            original = batch[i].cpu().permute(1, 2, 0).numpy()
            original = np.clip(original, 0, 1)
            axes[0, i].imshow(original)
            axes[0, i].set_title(f'Original {i+1}')
            axes[0, i].axis('off')
            
            # 重建图
            recon = reconstructed[i].cpu().permute(1, 2, 0).numpy()
            recon = np.clip(recon, 0, 1)
            axes[1, i].imshow(recon)
            axes[1, i].set_title(f'Reconstructed {i+1}')
            axes[1, i].axis('off')
        
        plt.tight_layout()
        plt.savefig(f'{save_dir}/epoch_{epoch:03d}_samples.png', dpi=150, bbox_inches='tight')
        plt.close()

def train_epoch(model, train_loader, criterion, optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    
    total_loss = 0
    total_metrics = {'mse': 0, 'ssim': 0}
    num_batches = len(train_loader)
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch}')
    
    for batch_idx, batch in enumerate(pbar):
        batch = batch.to(device)
        
        # 前向传播
        optimizer.zero_grad()
        reconstructed = model(batch, training=True)
        
        # 计算损失
        loss, loss_dict = criterion(reconstructed, batch)
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        for key in total_metrics:
            if key in loss_dict:
                total_metrics[key] += loss_dict[key]
        
        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'MSE': f'{loss_dict.get("mse", 0):.4f}',
            'SSIM': f'{loss_dict.get("ssim", 0):.4f}'
        })
    
    # 计算平均值
    avg_loss = total_loss / num_batches
    avg_metrics = {k: v / num_batches for k, v in total_metrics.items()}
    
    return avg_loss, avg_metrics

def validate(model, val_loader, criterion, metrics_calc, device):
    """验证"""
    model.eval()
    
    total_loss = 0
    total_metrics = {'mse': 0, 'psnr': 0, 'ssim': 0, 'mae': 0}
    num_batches = len(val_loader)
    
    with torch.no_grad():
        for batch in tqdm(val_loader, desc='Validating'):
            batch = batch.to(device)
            
            # 前向传播
            reconstructed = model(batch, training=False)
            
            # 计算损失
            loss, _ = criterion(reconstructed, batch)
            total_loss += loss.item()
            
            # 计算指标
            metrics = metrics_calc(reconstructed, batch)
            for key in total_metrics:
                if key in metrics:
                    total_metrics[key] += metrics[key]
    
    # 计算平均值
    avg_loss = total_loss / num_batches
    avg_metrics = {k: v / num_batches for k, v in total_metrics.items()}
    
    return avg_loss, avg_metrics

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--resume', type=str, help='Resume from checkpoint')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    args = parser.parse_args()
    
    # 配置
    config = ImageJSCCConfig()
    if args.epochs:
        config.epochs = args.epochs
    if args.lr:
        config.learning_rate = args.lr
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # 创建目录
    os.makedirs(config.checkpoint_dir, exist_ok=True)
    os.makedirs(config.log_dir, exist_ok=True)
    os.makedirs('samples', exist_ok=True)
    
    # 数据加载器
    print("Loading datasets...")
    train_loader, val_loader = create_dataloaders(config)
    
    # 模型
    print("Creating model...")
    model = ImageJSCC(config).to(device)
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f'Total parameters: {total_params:,}')
    print(f'Trainable parameters: {trainable_params:,}')
    
    # 损失函数和优化器
    criterion = CombinedLoss(mse_weight=1.0, ssim_weight=0.5, perceptual_weight=0.0)
    optimizer = optim.Adam(model.parameters(), lr=config.learning_rate, weight_decay=config.weight_decay)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=config.epochs, eta_min=1e-6)
    
    # 指标计算器
    metrics_calc = MetricsCalculator()
    
    # TensorBoard
    writer = SummaryWriter(config.log_dir)
    
    # 恢复训练
    start_epoch = 0
    best_psnr = 0
    
    if args.resume and os.path.exists(args.resume):
        print(f"Resuming from {args.resume}")
        checkpoint = torch.load(args.resume)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
        best_psnr = checkpoint.get('best_psnr', 0)
    
    # 训练循环
    print("Starting training...")
    
    for epoch in range(start_epoch, config.epochs):
        # 训练
        train_loss, train_metrics = train_epoch(model, train_loader, criterion, optimizer, device, epoch)
        
        # 验证
        val_loss, val_metrics = validate(model, val_loader, criterion, metrics_calc, device)
        
        # 学习率调度
        scheduler.step()
        
        # 记录日志
        writer.add_scalar('Loss/Train', train_loss, epoch)
        writer.add_scalar('Loss/Val', val_loss, epoch)
        writer.add_scalar('PSNR/Val', val_metrics['psnr'], epoch)
        writer.add_scalar('SSIM/Val', val_metrics['ssim'], epoch)
        writer.add_scalar('Learning_Rate', optimizer.param_groups[0]['lr'], epoch)
        
        # 打印结果
        print(f'Epoch {epoch:3d}: Train Loss: {train_loss:.4f}, '
              f'Val Loss: {val_loss:.4f}, PSNR: {val_metrics["psnr"]:.2f}dB, '
              f'SSIM: {val_metrics["ssim"]:.4f}')
        
        # 保存样本图像
        if epoch % 5 == 0:
            save_sample_images(model, val_loader, epoch, 'samples', device)
        
        # 保存最佳模型
        if val_metrics['psnr'] > best_psnr:
            best_psnr = val_metrics['psnr']
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_psnr': best_psnr,
                'config': config
            }, f'{config.checkpoint_dir}/best_model.pth')
        
        # 定期保存检查点
        if epoch % 10 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_psnr': best_psnr,
                'config': config
            }, f'{config.checkpoint_dir}/checkpoint_epoch_{epoch}.pth')
    
    print(f'Training completed! Best PSNR: {best_psnr:.2f}dB')
    writer.close()

if __name__ == '__main__':
    main()

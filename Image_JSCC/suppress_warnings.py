#!/usr/bin/env python3
"""
抑制TensorFlow/CUDA警告的脚本
在导入TensorFlow之前运行此脚本
"""

import os
import warnings

def suppress_tf_warnings():
    """抑制TensorFlow和CUDA相关的警告"""
    
    # 抑制TensorFlow日志警告
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 0=INFO, 1=WARN, 2=ERROR, 3=FATAL
    
    # 抑制oneDNN优化警告
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
    
    # 抑制CUDA相关警告
    os.environ['CUDA_VISIBLE_DEVICES'] = '0,1'  # 明确指定GPU
    
    # 抑制Python警告
    warnings.filterwarnings('ignore', category=UserWarning)
    warnings.filterwarnings('ignore', category=FutureWarning)
    warnings.filterwarnings('ignore', category=DeprecationWarning)
    
    print("✅ TensorFlow/CUDA警告已抑制")

if __name__ == "__main__":
    suppress_tf_warnings()

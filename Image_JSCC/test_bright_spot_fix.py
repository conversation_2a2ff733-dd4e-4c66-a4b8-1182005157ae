#!/usr/bin/env python3
"""
测试亮斑点修复效果的脚本
"""

import os
import sys
import numpy as np
import tensorflow as tf
import matplotlib.pyplot as plt
from PIL import Image

# 添加项目路径
sys.path.append('/home/<USER>/GESCO/Image_JSCC')

from config import Config
from models.image_jscc_model import ImageJSCCModel
from data.image_dataset import get_image_dataset

def test_model_output_range(model, test_batch):
    """测试模型输出范围"""
    print("🔍 测试模型输出范围...")
    
    # 前向传播
    output = model(test_batch, training=False)
    
    # 统计输出范围
    min_val = tf.reduce_min(output).numpy()
    max_val = tf.reduce_max(output).numpy()
    mean_val = tf.reduce_mean(output).numpy()
    std_val = tf.math.reduce_std(output).numpy()
    
    print(f"输出范围: [{min_val:.6f}, {max_val:.6f}]")
    print(f"均值: {mean_val:.6f}")
    print(f"标准差: {std_val:.6f}")
    
    # 检查是否有异常值
    if max_val > 1.1 or min_val < -0.1:
        print("⚠️  警告：输出超出正常范围 [0, 1]")
        return False
    
    # 检查是否有极端亮点
    bright_pixels = tf.reduce_sum(tf.cast(output > 0.95, tf.float32)).numpy()
    total_pixels = tf.size(output).numpy()
    bright_ratio = bright_pixels / total_pixels
    
    print(f"极亮像素比例: {bright_ratio:.6f} ({bright_pixels}/{total_pixels})")
    
    if bright_ratio > 0.01:  # 如果超过1%的像素过亮
        print("⚠️  警告：检测到过多极亮像素，可能存在亮斑点问题")
        return False
    
    print("✅ 输出范围正常")
    return True

def visualize_reconstruction(model, test_batch, save_path="test_reconstruction.png"):
    """可视化重建结果"""
    print("🎨 生成重建结果可视化...")
    
    # 获取重建结果
    reconstructed = model(test_batch, training=False)
    
    # 选择前4张图像进行可视化
    num_images = min(4, test_batch.shape[0])
    
    fig, axes = plt.subplots(2, num_images, figsize=(num_images * 4, 8))
    if num_images == 1:
        axes = axes.reshape(2, 1)
    
    for i in range(num_images):
        # 原图
        original = test_batch[i].numpy()
        axes[0, i].imshow(original)
        axes[0, i].set_title(f'Original {i+1}')
        axes[0, i].axis('off')
        
        # 重建图
        recon = reconstructed[i].numpy()
        # 确保在[0,1]范围内
        recon = np.clip(recon, 0, 1)
        axes[1, i].imshow(recon)
        axes[1, i].set_title(f'Reconstructed {i+1}')
        axes[1, i].axis('off')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 可视化结果已保存到: {save_path}")

def check_gradient_flow(model, test_batch):
    """检查梯度流"""
    print("📊 检查梯度流...")
    
    with tf.GradientTape() as tape:
        output = model(test_batch, training=True)
        # 简单的MSE损失
        loss = tf.reduce_mean(tf.square(output - test_batch))
    
    # 计算梯度
    gradients = tape.gradient(loss, model.trainable_variables)
    
    # 统计梯度
    grad_norms = []
    for grad in gradients:
        if grad is not None:
            grad_norm = tf.norm(grad).numpy()
            grad_norms.append(grad_norm)
    
    if grad_norms:
        max_grad = max(grad_norms)
        min_grad = min(grad_norms)
        mean_grad = np.mean(grad_norms)
        
        print(f"梯度范围: [{min_grad:.6f}, {max_grad:.6f}]")
        print(f"平均梯度: {mean_grad:.6f}")
        
        if max_grad > 10.0:
            print("⚠️  警告：检测到梯度爆炸")
            return False
        elif max_grad < 1e-8:
            print("⚠️  警告：检测到梯度消失")
            return False
        else:
            print("✅ 梯度流正常")
            return True
    else:
        print("❌ 无法计算梯度")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试亮斑点修复效果...")
    
    # 加载配置
    config = Config()
    
    # 创建模型
    print("📦 创建模型...")
    model = ImageJSCCModel(config)
    
    # 获取测试数据
    print("📊 加载测试数据...")
    test_dataset = get_image_dataset(config, batch_size=4, dataset_type='val')
    test_batch = next(iter(test_dataset))
    
    print(f"测试批次形状: {test_batch.shape}")
    
    # 运行测试
    tests_passed = 0
    total_tests = 3
    
    # 测试1: 输出范围
    if test_model_output_range(model, test_batch):
        tests_passed += 1
    
    # 测试2: 梯度流
    if check_gradient_flow(model, test_batch):
        tests_passed += 1
    
    # 测试3: 可视化（总是通过）
    try:
        visualize_reconstruction(model, test_batch)
        tests_passed += 1
        print("✅ 可视化测试通过")
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
    
    # 总结
    print(f"\n📋 测试总结: {tests_passed}/{total_tests} 测试通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！亮斑点问题应该已经修复。")
    else:
        print("⚠️  部分测试失败，可能需要进一步调整。")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
测试build方法修复
"""

import tensorflow as tf
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import ImageJSCCConfig
from model.image_jscc_model import create_model

def test_build_warnings():
    """测试build方法警告是否修复"""
    print("🔧 测试build方法修复...")
    
    config = ImageJSCCConfig()
    print(f"配置: 压缩比={config.compression_ratio}")
    
    # 创建模型 - 这里应该不再有build警告
    print("创建模型...")
    model = create_model(config)
    print("✅ 模型创建完成")
    
    # 显示模型摘要
    model.summary_custom()
    
    # 测试前向传播
    print("\n测试前向传播...")
    test_input = tf.random.uniform([2, config.image_height, config.image_width, config.image_channels])
    
    # 第一次调用
    output1 = model(test_input, training=False)
    print(f"✅ 第一次前向传播成功，输出形状: {output1.shape}")
    
    # 第二次调用
    output2 = model(test_input, training=True)
    print(f"✅ 第二次前向传播成功，输出形状: {output2.shape}")
    
    # 检查输出质量
    print(f"输出范围: [{tf.reduce_min(output1):.3f}, {tf.reduce_max(output1):.3f}]")
    
    return True

def test_compression_ratio():
    """测试新的压缩比"""
    print("\n📊 测试新的压缩比...")
    
    config = ImageJSCCConfig()
    
    # 计算压缩比
    original_size = config.image_height * config.image_width * config.image_channels * 8
    compressed_features = (config.image_height // 8) * (config.image_width // 8) // config.compression_ratio
    compressed_size = compressed_features * config.quantization_bits
    actual_compression_ratio = original_size / compressed_size
    
    print(f"原始图像: {config.image_height}×{config.image_width}×{config.image_channels} = {original_size:,} bits")
    print(f"压缩特征: {compressed_features} 维")
    print(f"压缩后: {compressed_size:,} bits")
    print(f"压缩比: {actual_compression_ratio:.1f}x")
    
    if actual_compression_ratio < 100:
        print("✅ 压缩比合理")
        return True
    elif actual_compression_ratio < 500:
        print("🔶 压缩比适中")
        return True
    else:
        print("⚠️  压缩比较高")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Build方法修复测试")
    print("=" * 60)
    
    try:
        # 测试压缩比
        compression_ok = test_compression_ratio()
        
        # 测试build警告修复
        build_ok = test_build_warnings()
        
        if build_ok and compression_ok:
            print("\n🎉 修复成功！")
            print("✅ Build警告已修复")
            print("✅ 压缩比合理")
            print("\n建议:")
            print("1. 现在可以进行正常训练")
            print("2. 监控训练过程中的指标")
            print("3. 如果重建质量仍不理想，可以进一步调整参数")
        else:
            print("\n🔶 部分修复成功")
            if not build_ok:
                print("❌ Build警告仍存在")
            if not compression_ok:
                print("❌ 压缩比仍需调整")
                
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

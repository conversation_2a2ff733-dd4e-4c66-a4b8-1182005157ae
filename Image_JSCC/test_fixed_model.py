#!/usr/bin/env python3
"""
测试修复后的模型
"""

import tensorflow as tf
import sys
import os
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import ImageJSCCConfig
from model.image_jscc_model import create_model
from data.image_dataset import get_image_dataset
from loss.image_loss import create_loss_function, ImageMetrics

def test_model_creation():
    """测试模型创建和参数统计"""
    print("🔧 测试模型创建...")
    
    config = ImageJSCCConfig()
    print(f"新配置: 压缩比={config.compression_ratio}")
    
    # 创建模型
    model = create_model(config)
    print("✅ 模型创建成功")
    
    # 显示模型摘要
    model.summary_custom()
    
    return model, config

def test_training_step():
    """测试训练步骤"""
    print("\n🚀 测试训练步骤...")
    
    model, config = test_model_creation()
    
    # 创建损失函数
    loss_fn = create_loss_function('mse')
    metrics_fn = ImageMetrics()
    
    # 创建优化器
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
    
    # 创建测试数据
    test_batch = tf.random.uniform([2, config.image_height, config.image_width, config.image_channels])
    print(f"测试批次形状: {test_batch.shape}")
    
    # 定义训练步骤
    @tf.function
    def train_step(model, optimizer, loss_fn, x_batch):
        with tf.GradientTape() as tape:
            y_pred = model(x_batch, training=True)
            loss = loss_fn(x_batch, y_pred)
        
        gradients = tape.gradient(loss, model.trainable_variables)
        optimizer.apply_gradients(zip(gradients, model.trainable_variables))
        
        return loss, y_pred
    
    try:
        # 执行训练步骤
        print("执行第一次训练步骤...")
        loss1, pred1 = train_step(model, optimizer, loss_fn, test_batch)
        print(f"✅ 第一次训练成功，损失: {loss1:.6f}")
        
        print("执行第二次训练步骤...")
        loss2, pred2 = train_step(model, optimizer, loss_fn, test_batch)
        print(f"✅ 第二次训练成功，损失: {loss2:.6f}")
        
        # 检查输出
        print(f"预测输出形状: {pred1.shape}")
        print(f"输出范围: [{tf.reduce_min(pred1):.3f}, {tf.reduce_max(pred1):.3f}]")
        
        # 计算指标
        metrics = metrics_fn(test_batch, pred1)
        print(f"PSNR: {metrics['psnr']:.2f} dB")
        print(f"SSIM: {metrics['ssim']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练步骤失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_compression_ratio():
    """测试新的压缩比"""
    print("\n📊 测试压缩比...")
    
    config = ImageJSCCConfig()
    
    # 计算理论压缩比
    original_size = config.image_height * config.image_width * config.image_channels * 8  # bits
    compressed_features = (config.image_height // 8) * (config.image_width // 8) // config.compression_ratio
    compressed_size = compressed_features * config.quantization_bits  # bits
    actual_compression_ratio = original_size / compressed_size
    
    print(f"原始图像大小: {config.image_height}×{config.image_width}×{config.image_channels} = {original_size:,} bits")
    print(f"压缩特征维度: {compressed_features}")
    print(f"压缩后大小: {compressed_size:,} bits")
    print(f"实际压缩比: {actual_compression_ratio:.1f}x")
    
    if actual_compression_ratio < 50:
        print("✅ 压缩比合理，应该能获得较好的重建质量")
    elif actual_compression_ratio < 100:
        print("🔶 压缩比适中，需要平衡质量和压缩率")
    else:
        print("⚠️  压缩比仍然较高，可能影响重建质量")

def quick_training_test():
    """快速训练测试"""
    print("\n🏋️ 快速训练测试...")
    
    model, config = test_model_creation()
    
    # 创建损失和指标函数
    loss_fn = create_loss_function('mse')
    metrics_fn = ImageMetrics()
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
    
    # 创建测试数据
    test_batch = tf.random.uniform([4, config.image_height, config.image_width, config.image_channels])
    
    # 训练前测试
    print("训练前:")
    pred_before = model(test_batch, training=False)
    loss_before = loss_fn(test_batch, pred_before)
    metrics_before = metrics_fn(test_batch, pred_before)
    print(f"  损失: {loss_before:.6f}")
    print(f"  PSNR: {metrics_before['psnr']:.2f} dB")
    print(f"  SSIM: {metrics_before['ssim']:.4f}")
    
    # 快速训练
    @tf.function
    def train_step(x_batch):
        with tf.GradientTape() as tape:
            y_pred = model(x_batch, training=True)
            loss = loss_fn(x_batch, y_pred)
        
        gradients = tape.gradient(loss, model.trainable_variables)
        optimizer.apply_gradients(zip(gradients, model.trainable_variables))
        return loss
    
    print("开始快速训练...")
    for step in range(20):
        loss = train_step(test_batch)
        if step % 5 == 0:
            print(f"  步骤 {step}: 损失 = {loss:.6f}")
    
    # 训练后测试
    print("训练后:")
    pred_after = model(test_batch, training=False)
    loss_after = loss_fn(test_batch, pred_after)
    metrics_after = metrics_fn(test_batch, pred_after)
    print(f"  损失: {loss_after:.6f}")
    print(f"  PSNR: {metrics_after['psnr']:.2f} dB")
    print(f"  SSIM: {metrics_after['ssim']:.4f}")
    
    # 检查改善
    psnr_improvement = metrics_after['psnr'] - metrics_before['psnr']
    print(f"PSNR改善: {psnr_improvement:.2f} dB")
    
    if psnr_improvement > 5:
        print("✅ 模型能够正常学习！")
        return True
    elif psnr_improvement > 1:
        print("🔶 模型有改善，但可能需要更多训练")
        return True
    else:
        print("❌ 模型学习效果不佳")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("修复后的模型测试")
    print("=" * 60)
    
    try:
        # 测试压缩比
        test_compression_ratio()
        
        # 测试训练步骤
        training_success = test_training_step()
        
        if training_success:
            # 快速训练测试
            learning_success = quick_training_test()
            
            if learning_success:
                print("\n🎉 所有测试通过！模型已修复")
                print("建议:")
                print("1. 使用新配置进行完整训练")
                print("2. 监控训练过程中的PSNR和SSIM指标")
                print("3. 如果效果仍不理想，可以进一步降低压缩比")
            else:
                print("\n🔶 模型创建成功，但学习效果需要改善")
                print("建议:")
                print("1. 进一步降低压缩比")
                print("2. 调整学习率")
                print("3. 增加模型容量")
        else:
            print("\n❌ 训练步骤仍有问题，需要进一步调试")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

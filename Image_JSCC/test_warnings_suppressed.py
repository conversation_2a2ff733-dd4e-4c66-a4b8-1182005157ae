#!/usr/bin/env python3
"""
测试警告抑制效果
"""

# 在导入TensorFlow之前设置环境变量
import os
import warnings

# 抑制所有警告
warnings.filterwarnings('ignore')

# 抑制TensorFlow和CUDA警告
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 抑制所有TensorFlow日志
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # 抑制oneDNN优化警告

# 现在导入TensorFlow
import tensorflow as tf
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import ImageJSCCConfig
from model.image_jscc_model import create_model

def test_clean_output():
    """测试是否有干净的输出（无警告）"""
    print("🧪 测试警告抑制效果...")
    print("如果看到CUDA/cuDNN警告，说明抑制不完全")
    print("-" * 50)
    
    # 创建配置
    config = ImageJSCCConfig()
    
    # 创建模型（这里通常会产生警告）
    print("创建模型...")
    model = create_model(config)
    
    # 测试前向传播
    print("测试前向传播...")
    test_input = tf.random.uniform([1, config.image_height, config.image_width, config.image_channels])
    output = model(test_input, training=False)
    
    print(f"✅ 测试完成，输出形状: {output.shape}")
    print("-" * 50)
    print("如果上面没有看到CUDA/cuDNN警告，说明抑制成功！")

if __name__ == "__main__":
    test_clean_output()

{"time":"2025-08-05T13:36:21.502414011+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T13:36:21.613802356+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T13:36:21.622534224+08:00","level":"INFO","msg":"stream: created new stream","id":"8ydkkh7j"}
{"time":"2025-08-05T13:36:21.622546154+08:00","level":"INFO","msg":"stream: started","id":"8ydkkh7j"}
{"time":"2025-08-05T13:36:21.62265452+08:00","level":"INFO","msg":"handler: started","stream_id":"8ydkkh7j"}
{"time":"2025-08-05T13:36:21.622673263+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"8ydkkh7j"}
{"time":"2025-08-05T13:36:21.622713217+08:00","level":"INFO","msg":"sender: started","stream_id":"8ydkkh7j"}
{"time":"2025-08-05T13:36:21.623659687+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-05T13:36:32.740251434+08:00","level":"INFO","msg":"stream: closing","id":"8ydkkh7j"}
{"time":"2025-08-05T13:36:32.740572578+08:00","level":"INFO","msg":"handler: closed","stream_id":"8ydkkh7j"}
{"time":"2025-08-05T13:36:32.740604184+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"8ydkkh7j"}
{"time":"2025-08-05T13:36:32.740660503+08:00","level":"INFO","msg":"sender: closed","stream_id":"8ydkkh7j"}
{"time":"2025-08-05T13:36:32.740747793+08:00","level":"INFO","msg":"stream: closed","id":"8ydkkh7j"}

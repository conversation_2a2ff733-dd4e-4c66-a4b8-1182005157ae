threadpoolctl==2.2.0
QtPy==2.2.0
conda_package_streaming==0.7.0
beautifulsoup4==4.11.1
cryptography==39.0.1
h11==0.16.0
networkx==2.8.4
qtconsole==5.4.0
charset-normalizer==2.0.4
whatthepatch==1.0.2
pycparser==2.21
mccabe==0.7.0
lazy-object-proxy==1.6.0
sionna==1.1.0
sphinxcontrib-jsmath==1.0.1
ipython-genutils==0.2.0
backcall==0.2.0
pytoolconfig==1.2.5
pycosat==0.6.4
h5py==3.14.0
markdown-it-py==3.0.0
typing_extensions==4.14.0
backports.tempfile==1.0
pure-eval==0.2.2
httpcore==1.0.9
distributed==2022.7.0
ipydatawidgets==4.3.5
colorcet==3.0.1
tzdata==2025.2
cloudpickle==2.0.0
argon2-cffi-bindings==21.2.0
datashape==0.5.4
torch==2.7.1
bleach==4.1.0
matplotlib-inline==0.1.6
tldextract==3.2.0
pandocfilters==1.5.0
conda-package-handling==2.0.2
fonttools==4.25.0
mock==4.0.3
Scrapy==2.8.0
sphinxcontrib-serializinghtml==1.1.5
cssselect==1.1.0
attrs==22.1.0
GitPython==3.1.44
dask==2022.7.0
conda==23.3.1
mypy-extensions==0.4.3
jupyter==1.0.0
colorama==0.4.6
nvidia-cufft-cu12==********
incremental==21.3.0
navigator-updater==0.3.0
multipledispatch==0.6.0
autopep8==1.6.0
Pillow==9.4.0
brotlipy==0.7.0
jupyter_client==7.3.4
munkres==1.1.4
zict==2.1.0
keyring==23.4.0
pythreejs==2.4.2
nest-asyncio==1.5.6
pycryptodomex==3.23.0
sniffio==1.2.0
binaryornot==0.4.4
diff-match-patch==20200713
wrapt==1.14.1
text-unidecode==1.3
pkginfo==1.9.6
ipython==8.10.0
tensorboard-data-server==0.7.2
QtAwesome==1.2.2
DeepMIMOv3==0.2.8
hf-xet==1.1.5
tqdm==4.64.1
PyHamcrest==2.0.2
timm==0.5.4
ruamel-yaml-conda==0.17.21
statsmodels==0.13.5
llvmlite==0.39.1
soupsieve==2.3.2.post1
isort==5.9.3
jeepney==0.7.1
scikit-learn==1.2.1
sphinxcontrib-applehelp==1.0.2
zipp==3.11.0
astropy==5.1
protobuf==5.29.5
Flask==2.2.2
importlib_resources==6.5.2
idna==3.4
pytest==7.1.2
wandb==0.21.0
Bottleneck==1.5.0
conda-pack==0.6.0
jsonpointer==2.1
urllib3==1.26.14
nvidia-curand-cu12==*********
cytoolz==0.12.0
Markdown==3.4.1
blobfile==3.0.0
tornado==6.1
pathlib==1.0.1
plotly==5.9.0
python-snappy==0.6.1
glob2==0.7
nbformat==5.7.0
tokenizers==0.21.4
watchdog==2.1.6
json5==0.9.6
flake8==6.0.0
astunparse==1.6.3
opencv-python==*********
gast==0.6.0
future==0.18.3
namex==0.1.0
nvidia-cudnn-cu12==********
holoviews==1.15.4
PyDispatcher==2.0.5
httpx==0.28.1
imagecodecs==2021.8.26
sionna-rt==1.1.0
distro==1.9.0
xarray==2022.11.0
python-dateutil==2.8.2
SQLAlchemy==1.4.39
iniconfig==1.1.1
jsonpatch==1.32
cycler==0.11.0
sphinxcontrib-qthelp==1.0.3
tblib==1.7.0
lz4==3.1.3
click==8.0.4
prometheus-client==0.14.1
websocket-client==0.58.0
Send2Trash==1.8.0
qstylizer==0.2.2
requests-toolbelt==0.9.1
importlib-metadata==4.11.3
tomli==2.0.1
ipywidgets==8.1.7
python-dotenv==1.1.1
libclang==18.1.1
nbclient==0.5.13
msgpack==1.0.3
panel==0.14.3
yapf==0.31.0
mkl-random==1.2.2
entrypoints==0.4
docutils==0.18.1
traittypes==0.2.1
conda-verify==3.4.2
Babel==2.11.0
jupyter-console==6.6.2
PySocks==1.7.1
pluggy==1.0.0
pyct==0.5.0
nvidia-cusparse-cu12==********
TBB==0.2
nvidia-cufile-cu12==********
daal4py==2023.0.2
joblib==1.1.1
platformdirs==2.5.2
docstring-to-markdown==0.11
openai==1.93.0
python-lsp-jsonrpc==1.0.0
service-identity==18.1.0
greenlet==2.0.1
sphinxcontrib-devhelp==1.0.2
triton==3.3.1
setuptools==65.6.3
arrow==1.2.3
HeapDict==1.0.1
conda-content-trust==0.1.3
hvplot==0.8.2
backports.functools-lru-cache==1.6.4
tomlkit==0.11.1
PyWavelets==1.4.1
stack-data==0.2.0
nvidia-cuda-runtime-cu12==12.6.77
sortedcontainers==2.4.0
parsel==1.6.0
google-pasta==0.2.0
PyYAML==6.0
Twisted==22.2.0
pyerfa==2.0.0
pytorch-msssim==1.0.0
sympy==1.14.0
spyder-kernels==2.4.1
w3lib==1.21.0
tabulate==0.8.10
jiter==0.10.0
Pygments==2.19.2
anaconda-navigator==2.4.0
python-lsp-server==1.7.1
Rtree==1.0.1
gmpy2==2.1.2
wheel==0.38.4
torchvision==0.22.1
imageio==2.26.0
pandas==2.3.1
partd==1.2.0
decorator==5.1.1
executing==0.8.3
SecretStorage==3.3.1
jupyterlab-pygments==0.1.2
pylint-venv==2.3.0
toml==0.10.2
optree==0.16.0
numexpr==2.8.4
dill==0.3.6
boltons==23.0.0
textdistance==4.2.1
anaconda-project==0.11.1
rich==14.0.0
pep8==1.7.1
python-slugify==5.0.2
pyls-spyder==0.4.0
ipykernel==6.19.2
alabaster==0.7.12
easydict==1.13
nbconvert==6.5.4
prompt-toolkit==3.0.36
ujson==5.4.0
py==1.11.0
sip==6.6.2
jupyter-server==1.23.4
cffi==1.15.1
wurlitzer==3.0.2
pickleshare==0.7.5
nvidia-nvjitlink-cu12==12.6.85
backports.weakref==1.0.post1
fastrlock==0.8.3
three-merge==0.1.1
cupy-cuda12x==13.5.1
hyperlink==21.0.0
spyder==5.4.1
flatbuffers==25.2.10
grpcio==1.73.1
tensorflow-io-gcs-filesystem==0.37.1
pyxdg==0.27
tables==3.7.0
webencodings==0.5.1
scikit-learn-intelex==20230228.214242
conda-token==0.4.0
requests-file==1.5.1
mitsuba==3.6.2
toolz==0.12.0
Protego==0.1.16
ply==3.11
imagesize==1.4.1
chardet==4.0.0
notebook_shim==0.2.2
astroid==2.14.2
pydocstyle==6.3.0
pyrsistent==0.18.0
bcrypt==3.2.0
scikit-image==0.19.3
mistune==0.8.4
parso==0.8.3
gitdb==4.0.12
nvidia-cuda-cupti-cu12==12.6.80
kiwisolver==1.4.4
queuelib==1.5.0
nvidia-cusparselt-cu12==0.6.3
absl-py==2.3.0
jupyterlab==3.5.3
opt_einsum==3.4.0
maturin==1.9.2
smart-open==5.2.1
nvidia-cuda-nvrtc-cu12==12.6.77
bokeh==2.4.3
jellyfish==0.9.0
ruamel.yaml==0.17.21
Jinja2==3.1.2
keras==3.10.0
tensorflow==2.19.0
conda-build==3.24.0
compressai==1.2.0
nltk==3.7
mdurl==0.1.2
pyasn1-modules==0.2.8
jupyterlab_widgets==3.0.15
et-xmlfile==1.1.0
diffusers==0.34.0
ruamel.yaml.clib==0.2.6
debugpy==1.5.1
intake==0.6.7
einops==0.8.1
widgetsnbextension==4.0.14
typing-inspection==0.4.1
python-lsp-black==1.2.1
tifffile==2021.7.2
jsonschema==4.17.3
seaborn==0.12.2
contourpy==1.0.5
nvidia-cusolver-cu12==********
sphinxcontrib-htmlhelp==2.0.0
defusedxml==0.7.1
jedi==0.18.1
regex==2022.7.9
pyparsing==3.0.9
patsy==0.5.3
notebook==6.5.2
argon2-cffi==21.3.0
itemloaders==1.0.4
packaging==22.0
anyio==3.5.0
datashader==0.14.4
constantly==15.1.0
gensim==4.3.0
nvidia-cublas-cu12==********
huggingface-hub==0.34.3
zipf_encoding==0.1.0
pyzmq==23.2.0
pyasn1==0.4.8
locket==1.0.0
mkl-service==2.4.0
mkl-fft==1.3.1
certifi==2025.1.31
appdirs==1.4.4
QDarkStyle==3.0.2
conda-repo-cli==1.0.41
pexpect==4.8.0
libarchive-c==2.9
numba==0.56.4
ptyprocess==0.7.0
termcolor==3.1.0
itemadapter==0.3.0
Unidecode==1.2.0
psutil==5.9.0
smmap==5.0.2
lpips==0.1.4
filelock==3.9.0
Werkzeug==2.2.2
numpydoc==1.5.0
pyflakes==3.0.1
tinycss2==1.2.1
pyviz-comms==2.0.2
pytz==2022.7
pydantic==2.11.7
safetensors==0.5.3
scipy==1.15.3
pylint==2.16.2
pycodestyle==2.10.0
tensorboard==2.19.0
Automat==20.2.0
pathspec==0.10.3
annotated-types==0.7.0
param==1.12.3
imbalanced-learn==0.10.1
terminado==0.17.1
drjit==1.0.3
zope.interface==5.4.0
poyo==0.5.0
jmespath==0.10.0
pydantic_core==2.33.2
matplotlib==3.10.3
black==22.6.0
jupyterlab_server==2.19.0
fastjsonschema==2.16.2
accelerate==1.9.0
fsspec==2025.7.0
asttokens==2.0.5
rope==1.7.0
flit_core==3.6.0
snowballstemmer==2.2.0
lxml==4.9.1
nvidia-nvtx-cu12==12.6.77
tenacity==8.0.1
pyodbc==4.0.34
nvidia-nccl-cu12==2.26.2
pycurl==7.45.1
nbclassic==0.5.2
cookiecutter==1.7.3
pyOpenSSL==23.0.0
transformers==4.54.1
atomicwrites==1.4.0
wcwidth==0.2.5
anaconda-client==1.11.2
comm==0.2.2
pip==22.3.1
requests==2.28.1
six==1.16.0
jinja2-time==0.2.0
numpy==1.26.4
itsdangerous==2.0.1
traitlets==5.7.1
clyent==1.2.2
intervaltree==3.1.0
pooch==1.4.0
sentry-sdk==2.32.0
jupyter_core==5.2.0
MarkupSafe==2.1.1
openpyxl==3.0.10
PyJWT==2.4.0
zstandard==0.19.0
ml_dtypes==0.5.1
inflection==0.5.1
Sphinx==5.0.2
PyQt5-sip==12.11.0
mpmath==1.2.1

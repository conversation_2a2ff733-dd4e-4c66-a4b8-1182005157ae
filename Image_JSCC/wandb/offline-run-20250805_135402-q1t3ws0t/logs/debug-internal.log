{"time":"2025-08-05T13:54:03.143529749+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T13:54:03.254203606+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T13:54:03.254267664+08:00","level":"INFO","msg":"stream: created new stream","id":"q1t3ws0t"}
{"time":"2025-08-05T13:54:03.254279881+08:00","level":"INFO","msg":"stream: started","id":"q1t3ws0t"}
{"time":"2025-08-05T13:54:03.254356594+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"q1t3ws0t"}
{"time":"2025-08-05T13:54:03.254360468+08:00","level":"INFO","msg":"handler: started","stream_id":"q1t3ws0t"}
{"time":"2025-08-05T13:54:03.254395415+08:00","level":"INFO","msg":"sender: started","stream_id":"q1t3ws0t"}
{"time":"2025-08-05T13:54:03.255464998+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}

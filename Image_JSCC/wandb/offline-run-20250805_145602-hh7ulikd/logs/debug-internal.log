{"time":"2025-08-05T14:56:02.40662046+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T14:56:02.520346688+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T14:56:02.520429617+08:00","level":"INFO","msg":"stream: created new stream","id":"hh7ulikd"}
{"time":"2025-08-05T14:56:02.520442499+08:00","level":"INFO","msg":"stream: started","id":"hh7ulikd"}
{"time":"2025-08-05T14:56:02.520567346+08:00","level":"INFO","msg":"handler: started","stream_id":"hh7ulikd"}
{"time":"2025-08-05T14:56:02.520583019+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"hh7ulikd"}
{"time":"2025-08-05T14:56:02.520608288+08:00","level":"INFO","msg":"sender: started","stream_id":"hh7ulikd"}
{"time":"2025-08-05T14:56:02.521393195+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-05T14:56:09.539956208+08:00","level":"INFO","msg":"stream: closing","id":"hh7ulikd"}
{"time":"2025-08-05T14:56:09.540160904+08:00","level":"INFO","msg":"handler: closed","stream_id":"hh7ulikd"}
{"time":"2025-08-05T14:56:09.540176667+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"hh7ulikd"}
{"time":"2025-08-05T14:56:09.540202468+08:00","level":"INFO","msg":"sender: closed","stream_id":"hh7ulikd"}
{"time":"2025-08-05T14:56:09.540340575+08:00","level":"INFO","msg":"stream: closed","id":"hh7ulikd"}

{"time":"2025-08-05T15:06:24.934397828+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T15:06:25.041893352+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T15:06:25.041960702+08:00","level":"INFO","msg":"stream: created new stream","id":"r65x5hv8"}
{"time":"2025-08-05T15:06:25.041970761+08:00","level":"INFO","msg":"stream: started","id":"r65x5hv8"}
{"time":"2025-08-05T15:06:25.042028888+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"r65x5hv8"}
{"time":"2025-08-05T15:06:25.042044278+08:00","level":"INFO","msg":"handler: started","stream_id":"r65x5hv8"}
{"time":"2025-08-05T15:06:25.042071758+08:00","level":"INFO","msg":"sender: started","stream_id":"r65x5hv8"}
{"time":"2025-08-05T15:06:25.04295072+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-05T15:06:34.058283383+08:00","level":"INFO","msg":"stream: closing","id":"r65x5hv8"}
{"time":"2025-08-05T15:06:34.058567029+08:00","level":"INFO","msg":"handler: closed","stream_id":"r65x5hv8"}
{"time":"2025-08-05T15:06:34.058581538+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"r65x5hv8"}
{"time":"2025-08-05T15:06:34.058585815+08:00","level":"INFO","msg":"sender: closed","stream_id":"r65x5hv8"}
{"time":"2025-08-05T15:06:34.058631025+08:00","level":"INFO","msg":"stream: closed","id":"r65x5hv8"}

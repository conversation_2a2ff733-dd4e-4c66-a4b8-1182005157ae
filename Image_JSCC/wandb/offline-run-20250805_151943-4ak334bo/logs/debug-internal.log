{"time":"2025-08-05T15:19:44.129709304+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T15:19:44.239806187+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T15:19:44.239881309+08:00","level":"INFO","msg":"stream: created new stream","id":"4ak334bo"}
{"time":"2025-08-05T15:19:44.239893388+08:00","level":"INFO","msg":"stream: started","id":"4ak334bo"}
{"time":"2025-08-05T15:19:44.239927706+08:00","level":"INFO","msg":"sender: started","stream_id":"4ak334bo"}
{"time":"2025-08-05T15:19:44.239944607+08:00","level":"INFO","msg":"handler: started","stream_id":"4ak334bo"}
{"time":"2025-08-05T15:19:44.239927375+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"4ak334bo"}
{"time":"2025-08-05T15:19:44.240994636+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-05T15:19:52.256533345+08:00","level":"INFO","msg":"stream: closing","id":"4ak334bo"}
{"time":"2025-08-05T15:19:52.256895506+08:00","level":"INFO","msg":"handler: closed","stream_id":"4ak334bo"}
{"time":"2025-08-05T15:19:52.25691922+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"4ak334bo"}
{"time":"2025-08-05T15:19:52.256936172+08:00","level":"INFO","msg":"sender: closed","stream_id":"4ak334bo"}
{"time":"2025-08-05T15:19:52.257006733+08:00","level":"INFO","msg":"stream: closed","id":"4ak334bo"}

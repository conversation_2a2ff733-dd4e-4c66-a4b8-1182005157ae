{"time":"2025-08-05T15:24:56.50583083+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T15:24:56.616850121+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T15:24:56.616907665+08:00","level":"INFO","msg":"stream: created new stream","id":"luig5rod"}
{"time":"2025-08-05T15:24:56.616917481+08:00","level":"INFO","msg":"stream: started","id":"luig5rod"}
{"time":"2025-08-05T15:24:56.617019101+08:00","level":"INFO","msg":"handler: started","stream_id":"luig5rod"}
{"time":"2025-08-05T15:24:56.617091435+08:00","level":"INFO","msg":"sender: started","stream_id":"luig5rod"}
{"time":"2025-08-05T15:24:56.617189726+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"luig5rod"}
{"time":"2025-08-05T15:24:56.61809808+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}

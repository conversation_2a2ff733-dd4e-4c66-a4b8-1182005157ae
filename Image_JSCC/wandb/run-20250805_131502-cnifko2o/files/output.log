Configuration loaded:
  Image size: 256x512x3
  Batch size: 4
  Epochs: 300
  Compression ratio: 1
  SNR: 10 dB
  Channel type: AWGN

Loading datasets...
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Found 500 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val
Steps per epoch: 743

Creating model...
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_encoder', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_decoder', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
============================================================
Image JSCC Model Summary
============================================================
Input shape: (256, 512, 3)
Compression ratio: 1
Quantization bits: 6
Channel type: AWGN
SNR: 10 dB
Compressed features: 2048
Transmitted bits: 12288
Actual compression ratio: 256.00
============================================================
Encoder parameters: 4,669,120
Decoder parameters: 537,305,859
Total trainable parameters: 541,974,979
============================================================

Starting training...
Logs will be saved to: /home/<USER>/GESCO/Image_JSCC/logs/ImageJSCC_snr_10dB_comp_1_20250805_131501/
Checkpoints will be saved to: /home/<USER>/GESCO/Image_JSCC/checkpoints/ImageJSCC_snr_10dB_comp_1_20250805_131501/

Epoch 1/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████████████████████████████████████████████████████| 743/743 [03:45<00:00,  3.29it/s, loss=0.3124]

Epoch 2/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████████████████████████████████| 743/743 [03:34<00:00,  3.47it/s, loss=0.2905]

Epoch 3/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████████████████████████████████| 743/743 [03:33<00:00,  3.48it/s, loss=0.2753]

Epoch 4/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████████████████████████████████| 743/743 [03:33<00:00,  3.48it/s, loss=0.2396]

Epoch 5/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████████████████████████████████| 743/743 [03:33<00:00,  3.48it/s, loss=0.2813]
Evaluating...
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 326, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 261, in main
    val_loss, val_metrics = evaluate_model(
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 78, in evaluate_model
    total_loss += batch_loss * tf.shape(x_batch)[0]
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/util/traceback_utils.py", line 153, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 6006, in raise_from_not_ok_status
    raise core._status_to_exception(e) from None  # pylint: disable=protected-access
tensorflow.python.framework.errors_impl.InvalidArgumentError: cannot compute Mul as input #1(zero-based) was expected to be a float tensor but is a int32 tensor [Op:Mul] name:
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 326, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 261, in main
    val_loss, val_metrics = evaluate_model(
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 78, in evaluate_model
    total_loss += batch_loss * tf.shape(x_batch)[0]
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/util/traceback_utils.py", line 153, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 6006, in raise_from_not_ok_status
    raise core._status_to_exception(e) from None  # pylint: disable=protected-access
tensorflow.python.framework.errors_impl.InvalidArgumentError: cannot compute Mul as input #1(zero-based) was expected to be a float tensor but is a int32 tensor [Op:Mul] name:

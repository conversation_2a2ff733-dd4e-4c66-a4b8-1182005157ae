2025-08-05 13:34:17,899 INFO    MainThread:1174482 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-05 13:34:17,899 INFO    MainThread:1174482 [wandb_setup.py:_flush():80] Configure stats pid to 1174482
2025-08-05 13:34:17,899 INFO    MainThread:1174482 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-05 13:34:17,899 INFO    MainThread:1174482 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/GESCO/Image_JSCC/wandb/settings
2025-08-05 13:34:17,899 INFO    MainThread:1174482 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-05 13:34:17,899 INFO    MainThread:1174482 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_133417-z6qitgps/logs/debug.log
2025-08-05 13:34:17,900 INFO    MainThread:1174482 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_133417-z6qitgps/logs/debug-internal.log
2025-08-05 13:34:17,900 INFO    MainThread:1174482 [wandb_init.py:init():830] calling init triggers
2025-08-05 13:34:17,900 INFO    MainThread:1174482 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'image_height': 256, 'image_width': 512, 'batch_size': 4, 'epochs': 300, 'learning_rate': 5e-05, 'compression_ratio': 1, 'quantization_bits': 6, 'snr_dB': 10, 'channel_type': 'AWGN', '_wandb': {}}
2025-08-05 13:34:17,900 INFO    MainThread:1174482 [wandb_init.py:init():871] starting backend
2025-08-05 13:34:18,106 INFO    MainThread:1174482 [wandb_init.py:init():874] sending inform_init request
2025-08-05 13:34:18,108 INFO    MainThread:1174482 [wandb_init.py:init():882] backend started and connected
2025-08-05 13:34:18,109 INFO    MainThread:1174482 [wandb_init.py:init():953] updated telemetry
2025-08-05 13:34:18,115 INFO    MainThread:1174482 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-05 13:34:49,115 INFO    Thread-1 (wrapped_target):1174482 [retry.py:__call__():173] [no run ID] Retry attempt failed:
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/urllib3/util/connection.py", line 95, in create_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/urllib3/util/connection.py", line 85, in create_connection
    sock.connect(sa)
TimeoutError: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/urllib3/connection.py", line 358, in connect
    self.sock = conn = self._new_conn()
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/urllib3/connection.py", line 179, in _new_conn
    raise ConnectTimeoutError(
urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x7a0d2c1f0c70>, 'Connection to api.wandb.ai timed out. (connect timeout=20)')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/requests/adapters.py", line 489, in send
    resp = conn.urlopen(
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7a0d2c1f0c70>, 'Connection to api.wandb.ai timed out. (connect timeout=20)'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/lib/retry.py", line 134, in __call__
    result = self._call_fn(*args, **kwargs)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/internal/internal_api.py", line 397, in execute
    return self.client.execute(*args, **kwargs)  # type: ignore
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 52, in execute
    result = self._get_result(document, *args, **kwargs)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/vendor/gql-0.2.0/wandb_gql/client.py", line 60, in _get_result
    return self.transport.execute(document, *args, **kwargs)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/lib/gql_request.py", line 58, in execute
    request = self.session.post(self.url, **post_args)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/requests/sessions.py", line 635, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/requests/sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/requests/sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/requests/adapters.py", line 553, in send
    raise ConnectTimeout(e, request=request)
requests.exceptions.ConnectTimeout: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7a0d2c1f0c70>, 'Connection to api.wandb.ai timed out. (connect timeout=20)'))
2025-08-05 13:35:46,333 WARNING MainThread:1174482 [wandb_init.py:init():1610] [no run ID] interrupted
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/wandb_init.py", line 1606, in init
    return wi.init(run_settings, run_config, run_printer)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/wandb_init.py", line 996, in init
    result = wait_with_progress(
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/mailbox/wait_with_progress.py", line 24, in wait_with_progress
    return wait_all_with_progress(
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/mailbox/wait_with_progress.py", line 87, in wait_all_with_progress
    return asyncio_compat.run(progress_loop_with_timeout)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/lib/asyncio_compat.py", line 30, in run
    return future.result()
  File "/home/<USER>/anaconda3/lib/python3.10/concurrent/futures/_base.py", line 453, in result
    self._condition.wait(timeout)
  File "/home/<USER>/anaconda3/lib/python3.10/threading.py", line 320, in wait
    waiter.acquire()
KeyboardInterrupt
2025-08-05 13:35:47,206 INFO    MsgRouterThr:1174482 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 2 handles.

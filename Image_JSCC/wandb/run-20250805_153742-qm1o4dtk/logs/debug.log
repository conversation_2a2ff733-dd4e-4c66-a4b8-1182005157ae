2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_setup.py:_flush():80] Configure stats pid to 1279600
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/GESCO/Image_JSCC/wandb/settings
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_153742-qm1o4dtk/logs/debug.log
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_153742-qm1o4dtk/logs/debug-internal.log
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_init.py:init():830] calling init triggers
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'image_height': 256, 'image_width': 512, 'batch_size': 4, 'epochs': 300, 'learning_rate': 5e-05, 'compression_ratio': 1, 'quantization_bits': 8, 'snr_dB': 10, 'channel_type': 'AWGN', '_wandb': {}}
2025-08-05 15:37:42,383 INFO    MainThread:1279600 [wandb_init.py:init():871] starting backend
2025-08-05 15:37:42,589 INFO    MainThread:1279600 [wandb_init.py:init():874] sending inform_init request
2025-08-05 15:37:42,591 INFO    MainThread:1279600 [wandb_init.py:init():882] backend started and connected
2025-08-05 15:37:42,593 INFO    MainThread:1279600 [wandb_init.py:init():953] updated telemetry
2025-08-05 15:37:42,598 INFO    MainThread:1279600 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout

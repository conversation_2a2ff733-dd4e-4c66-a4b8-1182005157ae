2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_setup.py:_flush():80] Configure stats pid to 1279600
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/GESCO/Image_JSCC/wandb/settings
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_153742-qm1o4dtk/logs/debug.log
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_153742-qm1o4dtk/logs/debug-internal.log
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_init.py:init():830] calling init triggers
2025-08-05 15:37:42,382 INFO    MainThread:1279600 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'image_height': 256, 'image_width': 512, 'batch_size': 4, 'epochs': 300, 'learning_rate': 5e-05, 'compression_ratio': 1, 'quantization_bits': 8, 'snr_dB': 10, 'channel_type': 'AWGN', '_wandb': {}}
2025-08-05 15:37:42,383 INFO    MainThread:1279600 [wandb_init.py:init():871] starting backend
2025-08-05 15:37:42,589 INFO    MainThread:1279600 [wandb_init.py:init():874] sending inform_init request
2025-08-05 15:37:42,591 INFO    MainThread:1279600 [wandb_init.py:init():882] backend started and connected
2025-08-05 15:37:42,593 INFO    MainThread:1279600 [wandb_init.py:init():953] updated telemetry
2025-08-05 15:37:42,598 INFO    MainThread:1279600 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-05 15:37:58,217 WARNING MainThread:1279600 [wandb_init.py:init():1610] [no run ID] interrupted
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/wandb_init.py", line 1606, in init
    return wi.init(run_settings, run_config, run_printer)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/wandb_init.py", line 996, in init
    result = wait_with_progress(
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/mailbox/wait_with_progress.py", line 24, in wait_with_progress
    return wait_all_with_progress(
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/mailbox/wait_with_progress.py", line 87, in wait_all_with_progress
    return asyncio_compat.run(progress_loop_with_timeout)
  File "/home/<USER>/anaconda3/lib/python3.10/site-packages/wandb/sdk/lib/asyncio_compat.py", line 30, in run
    return future.result()
  File "/home/<USER>/anaconda3/lib/python3.10/concurrent/futures/_base.py", line 453, in result
    self._condition.wait(timeout)
  File "/home/<USER>/anaconda3/lib/python3.10/threading.py", line 320, in wait
    waiter.acquire()
KeyboardInterrupt
2025-08-05 15:37:58,604 INFO    MsgRouterThr:1279600 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 2 handles.

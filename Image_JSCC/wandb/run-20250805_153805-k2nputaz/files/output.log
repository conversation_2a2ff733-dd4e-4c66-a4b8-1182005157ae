Configuration loaded:
  Image size: 256x512x3
  Batch size: 4
  Epochs: 300
  Compression ratio: 1
  SNR: 10 dB
  Channel type: AWGN

Loading datasets...
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Found 500 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val
Steps per epoch: 743

Creating model...
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_encoder', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_decoder', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_jscc_model', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
============================================================
Image JSCC Model Summary
============================================================
Input shape: (256, 512, 3)
Compression ratio: 1
Quantization bits: 8
Channel type: AWGN
SNR: 10 dB
Compressed features: 2048
Transmitted bits: 16384
Actual compression ratio: 192.00
============================================================
Encoder parameters: 2,191,488
Decoder parameters: 177,010,947
Total trainable parameters: 179,202,435
============================================================

Starting training...
Logs will be saved to: /home/<USER>/GESCO/Image_JSCC/logs/ImageJSCC_snr_10dB_comp_1_20250805_153804/
Checkpoints will be saved to: /home/<USER>/GESCO/Image_JSCC/checkpoints/ImageJSCC_snr_10dB_comp_1_20250805_153804/

Epoch 1/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|███████████████████████████████████████████████| 743/743 [01:00<00:00, 12.38it/s, loss=0.2481, grad=0.174]
Gradient Stats - Avg: 0.2366, Max: 1.8681, Min: 0.0238
Clipped Gradient Norm - Avg: 0.2366
Gradient Clipping Ratio: 8.34%

Epoch 2/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|███████████████████████████████████████████████| 743/743 [00:54<00:00, 13.54it/s, loss=0.2807, grad=0.047]
Gradient Stats - Avg: 0.1357, Max: 1.2408, Min: 0.0095
Clipped Gradient Norm - Avg: 0.1357
Gradient Clipping Ratio: 1.48%
Evaluating...
Validation - Loss: 0.3307, PSNR: 13.29, SSIM: 0.4394
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 373, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 338, in main
    model.save_weights_custom(best_model_path)
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 110, in save_weights_custom
    self.save_weights(filepath)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 122, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/saving/saving_api.py", line 227, in save_weights
    raise ValueError(
ValueError: The filename must end in `.weights.h5`. Received: filepath=/home/<USER>/GESCO/Image_JSCC/checkpoints/ImageJSCC_snr_10dB_comp_1_20250805_153804/best_model
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 373, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 338, in main
    model.save_weights_custom(best_model_path)
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 110, in save_weights_custom
    self.save_weights(filepath)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 122, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/saving/saving_api.py", line 227, in save_weights
    raise ValueError(
ValueError: The filename must end in `.weights.h5`. Received: filepath=/home/<USER>/GESCO/Image_JSCC/checkpoints/ImageJSCC_snr_10dB_comp_1_20250805_153804/best_model

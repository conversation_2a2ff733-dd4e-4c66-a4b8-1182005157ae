Configuration loaded:
  Image size: 256x512x3
  Batch size: 4
  Epochs: 300
  Compression ratio: 2
  SNR: 10 dB
  Channel type: AWGN

Loading datasets...
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Found 500 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val
Steps per epoch: 743

Creating model...
============================================================
Image JSCC Model Summary
============================================================
Input shape: (256, 512, 3)
Compression ratio: 2
Quantization bits: 8
Channel type: AWGN
SNR: 10 dB
Compressed features: 1024
Transmitted bits: 8192
Actual compression ratio: 384.00
============================================================
Encoder parameters: 1,928,320
Decoder parameters: 172,816,643
Total trainable parameters: 174,744,963
============================================================

Starting training...
Logs will be saved to: /home/<USER>/GESCO/Image_JSCC/logs/ImageJSCC_snr_10dB_comp_2_20250805_160910/
Checkpoints will be saved to: /home/<USER>/GESCO/Image_JSCC/checkpoints/ImageJSCC_snr_10dB_comp_2_20250805_160910/

Epoch 1/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|███████████████████████████████████████████████| 743/743 [00:59<00:00, 12.50it/s, loss=0.3274, grad=0.063]
Gradient Stats - Avg: 0.4196, Max: 32.9096, Min: 0.0390
Clipped Gradient Norm - Avg: 0.4196
Gradient Clipping Ratio: 16.55%

Epoch 2/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|███████████████████████████████████████████████| 743/743 [00:54<00:00, 13.63it/s, loss=0.2901, grad=0.071]
Gradient Stats - Avg: 0.4616, Max: 133.4831, Min: 0.0141
Clipped Gradient Norm - Avg: 0.4616
Gradient Clipping Ratio: 8.34%
Evaluating...
Validation - Loss: 0.3320, PSNR: 13.23, SSIM: 0.4380
Best model saved with validation loss: 0.3320

Epoch 3/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|███████████████████████████████████████████████| 743/743 [00:54<00:00, 13.66it/s, loss=0.2783, grad=0.249]
Gradient Stats - Avg: 0.0792, Max: 0.6146, Min: 0.0089
Clipped Gradient Norm - Avg: 0.0792
Gradient Clipping Ratio: 0.13%

Epoch 4/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|███████████████████████████████████████████████| 743/743 [00:54<00:00, 13.52it/s, loss=0.2645, grad=0.022]
Gradient Stats - Avg: 0.0814, Max: 1.9022, Min: 0.0106
Clipped Gradient Norm - Avg: 0.0814
Gradient Clipping Ratio: 0.40%
Evaluating...
Validation - Loss: 0.3311, PSNR: 13.29, SSIM: 0.4387
Best model saved with validation loss: 0.3311

Epoch 5/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|███████████████████████████████████████████████████| 743/743 [00:56<00:00, 13.15it/s, loss=0.2878, grad=0.310]
Gradient Stats - Avg: 0.1820, Max: 3.5392, Min: 0.0110
Clipped Gradient Norm - Avg: 0.1820
Gradient Clipping Ratio: 2.02%

Epoch 6/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|███████████████████████████████████████████████████| 743/743 [00:58<00:00, 12.63it/s, loss=0.2542, grad=0.151]
Gradient Stats - Avg: 0.1779, Max: 0.5995, Min: 0.0650
Clipped Gradient Norm - Avg: 0.1779
Gradient Clipping Ratio: 0.40%
Evaluating...
Validation - Loss: 0.3108, PSNR: 14.62, SSIM: 0.4538
Best model saved with validation loss: 0.3108

Epoch 7/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████| 743/743 [01:01<00:00, 12.13it/s, loss=0.2603, grad=0.175]
Gradient Stats - Avg: 0.1557, Max: 0.4446, Min: 0.0636
Clipped Gradient Norm - Avg: 0.1557
Gradient Clipping Ratio: 0.00%

Epoch 8/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████| 743/743 [01:02<00:00, 11.87it/s, loss=0.2536, grad=0.254]
Gradient Stats - Avg: 0.1540, Max: 3.0941, Min: 0.0576
Clipped Gradient Norm - Avg: 0.1540
Gradient Clipping Ratio: 0.13%
Evaluating...
Validation - Loss: 0.3088, PSNR: 14.62, SSIM: 0.4583
Best model saved with validation loss: 0.3088

Epoch 9/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████| 743/743 [01:03<00:00, 11.65it/s, loss=0.2714, grad=0.133]
Gradient Stats - Avg: 0.1609, Max: 3.6757, Min: 0.0640
Clipped Gradient Norm - Avg: 0.1609
Gradient Clipping Ratio: 0.27%

Epoch 10/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████| 743/743 [01:03<00:00, 11.71it/s, loss=0.2533, grad=0.117]
Gradient Stats - Avg: 0.1610, Max: 0.8634, Min: 0.0655
Clipped Gradient Norm - Avg: 0.1610
Gradient Clipping Ratio: 0.27%
Evaluating...
Validation - Loss: 0.3015, PSNR: 14.94, SSIM: 0.4657
Best model saved with validation loss: 0.3015

Epoch 11/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████| 743/743 [01:04<00:00, 11.50it/s, loss=0.2461, grad=0.154]
Gradient Stats - Avg: 0.1589, Max: 1.1417, Min: 0.0586
Clipped Gradient Norm - Avg: 0.1589
Gradient Clipping Ratio: 0.81%

Epoch 12/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training:  78%|▊| 581/743 [00:51<00:14, 11.23it/s, loss=0.2407, psnr=17.01, ssim=0.561, grad=0
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 373, in <module>
    model.save_weights_custom(final_model_path)
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 240, in main
    epoch_grad_norms = []
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 419, in numpy
    maybe_arr = self._numpy()  # pylint: disable=protected-access
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 385, in _numpy
    return self._numpy_internal()
KeyboardInterrupt
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 373, in <module>
    model.save_weights_custom(final_model_path)
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 240, in main
    epoch_grad_norms = []
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 419, in numpy
    maybe_arr = self._numpy()  # pylint: disable=protected-access
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 385, in _numpy
    return self._numpy_internal()
KeyboardInterrupt

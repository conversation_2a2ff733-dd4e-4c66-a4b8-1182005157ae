joblib==1.5.1
idna==3.10
google-auth-oauthlib==0.4.6
tensorflow-probability==0.11.0
sionna==1.1.0
Keras-Preprocessing==1.1.2
regex==2024.11.6
networkx==3.4.2
markdown-it-py==3.0.0
pyasn1==0.6.1
celluloid==0.2.0
ipydatawidgets==4.3.5
tzdata==2025.2
torch==2.7.1
stack_data==0.6.3
prompt_toolkit==3.0.50
transformers==4.49.0
tensorboard-plugin-wit==1.8.1
wheel==0.45.1
pybind11==3.0.0
pyzmq==26.2.0
mpmath==1.3.0
nvidia-cufft-cu12==********
lxml==6.0.0
jupyter-client==7.3.4
fsspec==2025.3.2
importlib_metadata==8.7.0
Jinja2==3.1.6
pythreejs==2.4.2
pycryptodomex==3.23.0
ipython==8.35.0
parso==0.8.4
text-unidecode==1.3
tensorboard-data-server==0.7.2
scikit-learn==1.7.1
wrapt==1.17.2
hf-xet==1.1.5
Werkzeug==3.1.3
inquirerpy==0.3.4
aiohttp==3.12.15
requests==2.32.3
importlib_resources==6.5.2
wandb==0.21.0
Bottleneck==1.5.0
nvidia-curand-cu12==*********
async-timeout==5.0.1
blobfile==3.0.0
tornado==6.1
Pygments==2.19.1
tokenizers==0.21.4
threadpoolctl==3.6.0
astunparse==1.6.3
opencv-python==*********
namex==0.1.0
nvidia-cudnn-cu12==********
gast==0.4.0
sionna-rt==1.1.0
GitPython==3.1.45
torch-geometric==2.6.1
rsa==4.9
Markdown==3.8
pexpect==4.9.0
gdown==5.2.0
libclang==18.1.1
entrypoints==0.4
propcache==0.3.2
pure_eval==0.2.3
traittypes==0.2.1
zipp==3.21.0
PySocks==1.7.1
nvidia-cusparse-cu12==********
nvidia-cufile-cu12==********
sentry-sdk==2.33.2
oauthlib==3.2.2
array_record==0.7.1
cachetools==5.5.2
executing==2.1.0
triton==3.3.1
MarkupSafe==3.0.2
jedi==0.19.2
pfzy==0.3.4
jsonpatch==1.33
nvidia-cuda-runtime-cu12==12.6.77
six==1.17.0
exceptiongroup==1.2.2
google-pasta==0.2.0
cloudpickle==1.3.0
lazy_loader==0.4
pytorch-msssim==1.0.0
tifffile==2025.5.10
sympy==1.14.0
filelock==3.18.0
pycocotools==2.0.10
setuptools==75.8.0
tensorflow-hub==0.16.1
torchvision==0.22.1
aiosignal==1.4.0
jsonpointer==3.0.0
imageio==2.37.0
pandas==2.3.1
hf_transfer==0.1.9
widgetsnbextension==4.0.13
jupyterlab_widgets==3.0.13
toml==0.10.2
debugpy==1.8.11
termcolor==3.0.1
optree==0.16.0
rich==14.0.0
pyasn1_modules==0.4.2
grpcio==1.71.0
easydict==1.13
etils==1.12.2
python-slugify==8.0.4
bleach==6.2.0
pickleshare==0.7.5
nvidia-nvjitlink-cu12==12.6.85
fastrlock==0.8.3
cupy-cuda12x==13.5.1
pyparsing==3.2.3
matplotlib-inline==0.1.7
contourpy==1.3.2
dm-tree==0.1.9
tensorflow-datasets==4.9.2
tomli==2.2.1
urllib3==2.3.0
flatbuffers==25.2.10
frozenlist==1.7.0
tensorflow-metadata==1.17.1
tensorflow-io-gcs-filesystem==0.37.1
kaggle==1.7.4.2
DeepMIMOv3==0.2.9
attrs==25.3.0
aiohappyeyeballs==2.6.1
mitsuba==3.6.2
platformdirs==4.3.7
gitdb==4.0.12
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cusparselt-cu12==0.6.3
pytz==2025.2
opt_einsum==3.4.0
compressai==1.2.8
nvidia-cuda-nvrtc-cu12==12.6.77
requests-oauthlib==2.0.0
fonttools==4.59.0
kiwisolver==1.4.8
multidict==6.6.3
keras==3.10.0
tensorflow==2.19.0
mdurl==0.1.2
asttokens==3.0.0
diffusers==0.34.0
promise==2.3
einops==0.8.1
typing-inspection==0.4.1
typing_extensions==4.13.1
nvidia-cusolver-cu12==********
jupyter_core==5.7.2
scikit-image==0.25.2
tensorflow-gan==2.1.0
beautifulsoup4==4.13.4
python-dateutil==2.9.0.post0
traitlets==5.14.3
tensorflow-estimator==2.9.0
nvidia-cublas-cu12==********
cycler==0.12.1
huggingface-hub==0.34.3
zipf_encoding==0.1.0
h5py==3.13.0
ffmpeg==1.4
certifi==2025.1.31
nest_asyncio==1.6.0
ptyprocess==0.7.0
psutil==5.9.0
smmap==5.0.2
charset-normalizer==3.4.1
lpips==0.1.4
appdirs==1.4.4
seaborn==0.13.2
ipykernel==6.29.5
pydantic==2.11.7
safetensors==0.5.3
PyYAML==6.0.2
yarl==1.20.1
scipy==1.15.3
tensorboard==2.19.0
tf-keras==2.15.0
annotated-types==0.7.0
packaging==24.2
protobuf==4.21.6
drjit==1.0.3
pydantic_core==2.33.2
matplotlib==3.10.3
websocket-client==1.8.0
accelerate==1.9.0
nvidia-nvtx-cu12==12.6.77
nvidia-nccl-cu12==2.26.2
google-auth==2.38.0
ipywidgets==8.1.5
click==8.1.8
wcwidth==0.2.13
soupsieve==2.7
webencodings==0.5.1
comm==0.2.2
pip==25.0
visdom==0.2.4
numpy==1.26.4
decorator==5.2.1
tqdm==4.67.1
pillow==11.3.0
ml_dtypes==0.5.1
absl-py==2.2.2

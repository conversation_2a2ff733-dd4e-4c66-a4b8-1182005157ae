Configuration loaded:
  Image size: 256x512x3
  Batch size: 4
  Epochs: 300
  Compression ratio: 2
  SNR: 10 dB
  Channel type: AWGN

Loading datasets...
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Found 500 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val
Steps per epoch: 743

Creating model...
============================================================
Image JSCC Model Summary
============================================================
Input shape: (256, 512, 3)
Compression ratio: 2
Quantization bits: 8
Channel type: AWGN
SNR: 10 dB
Compressed features: 1024
Transmitted bits: 8192
Actual compression ratio: 384.00
============================================================
Encoder parameters: 1,928,320
Decoder parameters: 172,816,643
Total trainable parameters: 174,744,963
============================================================

Starting training...
Logs will be saved to: /home/<USER>/GESCO/Image_JSCC/logs/ImageJSCC_snr_10dB_comp_2_20250805_162125/
Checkpoints will be saved to: /home/<USER>/GESCO/Image_JSCC/checkpoints/ImageJSCC_snr_10dB_comp_2_20250805_162125/

Epoch 1/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████| 743/743 [01:11<00:00, 10.43it/s, loss=0.3035, grad=0.280]
Gradient Stats - Avg: 0.5614, Max: 4.2962, Min: 0.0534
Clipped Gradient Norm - Avg: 0.5614
Gradient Clipping Ratio: 95.69%

Epoch 2/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training: 100%|████████████████████| 743/743 [01:08<00:00, 10.86it/s, loss=0.2741, grad=0.226]
Gradient Stats - Avg: 0.3608, Max: 1.4876, Min: 0.0593
Clipped Gradient Norm - Avg: 0.3608
Gradient Clipping Ratio: 99.46%
Evaluating...
Validation - Loss: 0.3302, PSNR: 13.39, SSIM: 0.4383
Best model saved with validation loss: 0.3302

Epoch 3/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training:  30%|██████              | 225/743 [00:24<00:56,  9.15it/s, loss=0.2637, grad=0.206]
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 381, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 248, in main
    epoch_loss += loss.numpy()
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 419, in numpy
    maybe_arr = self._numpy()  # pylint: disable=protected-access
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 385, in _numpy
    return self._numpy_internal()
KeyboardInterrupt
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 381, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 248, in main
    epoch_loss += loss.numpy()
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 419, in numpy
    maybe_arr = self._numpy()  # pylint: disable=protected-access
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 385, in _numpy
    return self._numpy_internal()
KeyboardInterrupt

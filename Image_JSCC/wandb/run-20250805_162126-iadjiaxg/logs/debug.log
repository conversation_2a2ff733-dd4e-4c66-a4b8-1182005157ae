2025-08-05 16:21:26,180 INFO    MainThread:1326274 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-05 16:21:26,180 INFO    MainThread:1326274 [wandb_setup.py:_flush():80] Configure stats pid to 1326274
2025-08-05 16:21:26,180 INFO    MainThread:1326274 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-05 16:21:26,180 INFO    MainThread:1326274 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/GESCO/Image_JSCC/wandb/settings
2025-08-05 16:21:26,180 INFO    MainThread:1326274 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-05 16:21:26,180 INFO    MainThread:1326274 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_162126-iadjiaxg/logs/debug.log
2025-08-05 16:21:26,180 INFO    MainThread:1326274 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_162126-iadjiaxg/logs/debug-internal.log
2025-08-05 16:21:26,180 INFO    MainThread:1326274 [wandb_init.py:init():830] calling init triggers
2025-08-05 16:21:26,180 INFO    MainThread:1326274 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'image_height': 256, 'image_width': 512, 'batch_size': 4, 'epochs': 300, 'learning_rate': 1e-05, 'compression_ratio': 2, 'quantization_bits': 8, 'snr_dB': 10, 'channel_type': 'AWGN', '_wandb': {}}
2025-08-05 16:21:26,180 INFO    MainThread:1326274 [wandb_init.py:init():871] starting backend
2025-08-05 16:21:26,386 INFO    MainThread:1326274 [wandb_init.py:init():874] sending inform_init request
2025-08-05 16:21:26,388 INFO    MainThread:1326274 [wandb_init.py:init():882] backend started and connected
2025-08-05 16:21:26,389 INFO    MainThread:1326274 [wandb_init.py:init():953] updated telemetry
2025-08-05 16:21:26,394 INFO    MainThread:1326274 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-05 16:21:27,785 INFO    MainThread:1326274 [wandb_init.py:init():1029] starting run threads in backend
2025-08-05 16:21:27,942 INFO    MainThread:1326274 [wandb_run.py:_console_start():2458] atexit reg
2025-08-05 16:21:27,942 INFO    MainThread:1326274 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-05 16:21:27,942 INFO    MainThread:1326274 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-05 16:21:27,942 INFO    MainThread:1326274 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-05 16:21:27,943 INFO    MainThread:1326274 [wandb_init.py:init():1075] run started, returning control to user process
2025-08-05 16:24:20,959 INFO    MsgRouterThr:1326274 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
2025-08-05 16:24:21,370 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,373 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,373 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,373 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,373 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,374 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,374 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,374 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,374 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,374 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,374 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,374 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,375 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,375 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,375 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,375 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,375 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,375 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,375 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,375 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,376 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,376 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,376 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,376 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,376 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,377 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,377 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,377 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,377 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,377 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,377 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,378 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 16:24:21,378 ERROR   MainThread:1326274 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
